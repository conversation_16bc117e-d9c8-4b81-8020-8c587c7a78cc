    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Скрипты для админки -->
    <script>
    // Функция для копирования реферальной ссылки в админке
    function copyReferralLinkAdmin(userId) {
        const linkInput = document.getElementById('referralLink' + userId);
        if (!linkInput) {
            alert('Ошибка: Не найден элемент ссылки');
            return;
        }

        const textToCopy = linkInput.value;

        // Пытаемся использовать современный API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(textToCopy).then(() => {
                showCopySuccess(userId);
            }).catch((err) => {
                console.warn('Ошибка clipboard API:', err);
                fallbackCopy(linkInput, userId);
            });
        } else {
            // Fallback для старых браузеров
            fallbackCopy(linkInput, userId);
        }
    }

    // Fallback метод копирования
    function fallbackCopy(inputElement, userId) {
        try {
            inputElement.select();
            inputElement.setSelectionRange(0, 99999); // Для мобильных устройств
            document.execCommand('copy');
            showCopySuccess(userId);
        } catch (err) {
            console.error('Ошибка fallback копирования:', err);
            alert('Не удалось скопировать ссылку. Скопируйте вручную.');
        }
    }

    // Показать уведомление об успешном копировании
    function showCopySuccess(userId) {
        const button = document.querySelector(`button[onclick="copyReferralLinkAdmin('${userId}')"]`);
        if (button) {
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i>';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');

            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        }

        // Показываем временное уведомление
        const notification = document.createElement('div');
        notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="bi bi-check-circle"></i> Реферальная ссылка скопирована!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Автоматически удаляем уведомление через 3 секунды
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    </script>

</body>
</html>
