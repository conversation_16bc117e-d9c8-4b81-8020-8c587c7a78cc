<?php
/**
 * set_webhook.php
 * Установка webhook для Telegram бота
 */

require_once __DIR__ . '/config.php';

// Устанавливаем webhook
$result = setWebhook();

if ($result) {
    echo "✅ Webhook успешно установлен!<br>";
    echo "URL: " . WEBHOOK_URL . "<br>";
    echo "Результат: " . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} else {
    echo "❌ Ошибка при установке webhook!<br>";
    echo "Проверьте логи бота для получения подробной информации.";
}

// Проверяем информацию о webhook
echo "<br><br><h3>Информация о webhook:</h3>";
$webhookInfo = telegramRequest('getWebhookInfo');
if ($webhookInfo) {
    echo "<pre>" . json_encode($webhookInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
}
?>
