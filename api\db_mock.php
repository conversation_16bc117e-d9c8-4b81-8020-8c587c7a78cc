<?php
/**
 * api/db_mock.php
 * Эмуляция базы данных на JSON-файле (с уточнением лога ошибки сохранения)
 */
require_once __DIR__ . '/config.php';

// --- Функция loadUserData без изменений ---
function loadUserData(): array {
    error_log("loadUserData INFO: Загрузка данных из " . USER_DATA_FILE);
    if (!file_exists(USER_DATA_FILE)) { error_log("loadUserData WARNING: Файл не найден. Попытка создать."); if (file_put_contents(USER_DATA_FILE, '{}', LOCK_EX) === false) { error_log("loadUserData ERROR: Не удалось создать файл данных " . USER_DATA_FILE); return []; } @chmod(USER_DATA_FILE, 0664); error_log("loadUserData INFO: Файл данных создан."); return []; }
    $jsonData = file_get_contents(USER_DATA_FILE); if ($jsonData === false) { error_log("loadUserData ERROR: Не удалось прочитать файл " . USER_DATA_FILE); return []; } if (empty(trim($jsonData))) { error_log("loadUserData INFO: Файл пуст."); return []; }
    $data = json_decode($jsonData, true); if ($data === null && json_last_error() !== JSON_ERROR_NONE) { error_log("loadUserData ERROR: Ошибка декодирования JSON: ".json_last_error_msg()); return []; }
    if (is_array($data)) { foreach ($data as &$details) { if (!is_array($details)) { $details = ['balance' => 0, 'referrer_id' => null]; continue; } if (!isset($details['referrer_id'])) { $details['referrer_id'] = null; } if (!isset($details['balance']) || !is_numeric($details['balance'])) { $details['balance'] = 0; } } unset($details); } else { error_log("loadUserData WARNING: Данные не являются массивом после декодирования."); return []; }
    if (!is_array($data)) { error_log("loadUserData ERROR: Финальная проверка провалена, данные - не массив!"); return []; } error_log("loadUserData INFO: Данные успешно загружены."); return $data;
}

/**
 * Сохраняет данные всех пользователей в JSON-файл (с уточненным логированием ошибки).
 * @param array $userData Данные для сохранения.
 * @return bool true в случае успеха, false при ошибке.
 */
function saveUserData(array $userData): bool
{
    error_log("saveUserData INFO: Начало сохранения данных.");

    // 1. Проверка структуры данных перед кодированием (с детальным логом ошибки)
    foreach ($userData as $loopUserId => $loopDetails) {
         $errorDetail = '';
         if (!is_array($loopDetails)) {
              $errorDetail = 'Значение НЕ является массивом.';
         } elseif (!array_key_exists('balance', $loopDetails)) { // Используем array_key_exists для проверки null
             $errorDetail = 'Отсутствует ключ [balance].';
         } elseif (!array_key_exists('referrer_id', $loopDetails)) { // Используем array_key_exists
             $errorDetail = 'Отсутствует ключ [referrer_id].';
         }

         if (!empty($errorDetail)) {
              error_log("saveUserData ERROR: Обнаружены некорректные данные для пользователя $loopUserId ПЕРЕД кодированием. Проблема: $errorDetail. Структура: " . print_r($loopDetails, true));
              // Попытаемся исправить на лету (рискованно, но для отладки)
              // $userData[$loopUserId] = [
              //     'balance' => isset($loopDetails['balance']) && is_numeric($loopDetails['balance']) ? $loopDetails['balance'] : 0,
              //     'referrer_id' => $loopDetails['referrer_id'] ?? null
              // ];
              // error_log("saveUserData WARNING: Попытка исправить данные для $loopUserId.");
              // Если исправление не нужно, то возвращаем ошибку:
              return false;
         }
     }
    error_log("saveUserData INFO: Структура данных для сохранения проверена.");

    // ВАЖНО: Автоматически исправляем реферальные связи перед сохранением
    $fixed = fixReferralLinks($userData);
    if ($fixed) {
        error_log("saveUserData INFO: Реферальные связи автоматически исправлены перед сохранением.");
    }

    // 2. Кодирование в JSON
    $jsonData = json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($jsonData === false) { error_log("saveUserData ERROR: json_encode НЕ УДАЛСЯ! Ошибка: " . json_last_error_msg()); return false; }
    error_log("saveUserData INFO: json_encode успешен.");

    // 3. Проверка прав и запись (логирование как в прошлой версии)
    $filePath = USER_DATA_FILE; $dirPath = dirname($filePath);
    if (!is_writable($dirPath)) { error_log("saveUserData ERROR: Директория НЕ доступна для записи: " . $dirPath); return false; }
    if (file_exists($filePath) && !is_writable($filePath)) { error_log("saveUserData ERROR: Файл существует, но НЕ доступен для записи: " . $filePath); return false; }
    error_log("saveUserData INFO: Права на запись проверены. Попытка file_put_contents.");
    $bytesWritten = file_put_contents($filePath, $jsonData, LOCK_EX);

    // 4. Проверка результата записи
    if ($bytesWritten === false) { error_log("saveUserData CRITICAL ERROR: file_put_contents НЕ УДАЛСЯ!"); $lastError = error_get_last(); if ($lastError) { error_log("saveUserData DEBUG: Последняя ошибка PHP: " . print_r($lastError, true)); } return false; }

    error_log("saveUserData SUCCESS: Данные сохранены. Записано байт: " . $bytesWritten);
    return true;
}

// --- Функции getUserDetails, increaseUserBalance, getUserReferrerId с поддержкой никнеймов ---
/**
 * Получает или создает данные пользователя с ПОЛНОЙ структурой данных
 *
 * @param int $userId ID пользователя
 * @param array &$userData Массив данных всех пользователей
 * @param int|null $referrerIdFromParam ID реферера (если есть)
 * @param array|null $telegramUserData Данные пользователя из Telegram (если есть)
 * @return array Данные пользователя
 */
function getUserDetails(int $userId, array &$userData, ?int $referrerIdFromParam = null, ?array $telegramUserData = null): array {
    $isNewUser = !isset($userData[$userId]);

    if ($isNewUser) {
        // Проверяем валидность реферера
        $validReferrerId = null;
        if ($referrerIdFromParam !== null && $referrerIdFromParam !== $userId && isset($userData[$referrerIdFromParam])) {
            $validReferrerId = $referrerIdFromParam;
        }

        // Создаем пользователя с ПОЛНОЙ структурой данных
        $userData[$userId] = [
            // Основные поля
            'id' => $userId,
            'balance' => 0,
            'total_earned' => 0,
            'joined' => time(),

            // Реферальная система
            'referrer_id' => $validReferrerId,
            'referrals' => [],
            'referrals_count' => 0,
            'referral_earnings' => 0,

            // Система выводов
            'withdrawals' => [],
            'withdrawal_log' => [],
            'withdrawals_count' => 0,

            // Telegram данные
            'username' => null,
            'first_name' => null,
            'last_name' => null,
            'language' => 'ru',

            // Системные поля
            'registered_at' => time(),
            'last_activity' => time(),
            'suspicious_activity' => 0,
            'suspicious_activity_count' => 0,
            'blocked' => false
        ];

        error_log("db_mock INFO: User $userId created with FULL structure".($validReferrerId?" and referrer $validReferrerId":".")." Current userData count: ".count($userData));
    }

    // Обновляем данные пользователя из Telegram, если они предоставлены
    if ($telegramUserData !== null) {
        // Сохраняем имя пользователя
        if (isset($telegramUserData['username'])) {
            $userData[$userId]['username'] = $telegramUserData['username'];
        }

        // Сохраняем имя и фамилию
        if (isset($telegramUserData['first_name'])) {
            $userData[$userId]['first_name'] = $telegramUserData['first_name'];
        }

        if (isset($telegramUserData['last_name'])) {
            $userData[$userId]['last_name'] = $telegramUserData['last_name'];
        }

        error_log("db_mock INFO: Updated Telegram user data for user $userId");
    }

    // Обеспечиваем наличие всех обязательных полей
    if (!isset($userData[$userId]['id'])) {
        $userData[$userId]['id'] = $userId;
    }
    if (!isset($userData[$userId]['balance'])) {
        $userData[$userId]['balance'] = 0;
    }
    if (!isset($userData[$userId]['total_earned'])) {
        $userData[$userId]['total_earned'] = 0;
    }
    if (!isset($userData[$userId]['referrals'])) {
        $userData[$userId]['referrals'] = [];
    }
    if (!isset($userData[$userId]['referrals_count'])) {
        $userData[$userId]['referrals_count'] = count($userData[$userId]['referrals']);
    }
    if (!isset($userData[$userId]['referral_earnings'])) {
        $userData[$userId]['referral_earnings'] = 0;
    }
    if (!isset($userData[$userId]['withdrawals'])) {
        $userData[$userId]['withdrawals'] = [];
    }
    if (!isset($userData[$userId]['joined'])) {
        $userData[$userId]['joined'] = time();
    }
    if (!isset($userData[$userId]['suspicious_activity'])) {
        $userData[$userId]['suspicious_activity'] = 0;
    }
    if (!isset($userData[$userId]['blocked'])) {
        $userData[$userId]['blocked'] = false;
    }

    // Обеспечиваем наличие ВСЕХ полей для данных пользователя
    if (!isset($userData[$userId]['username'])) {
        $userData[$userId]['username'] = null;
    }
    if (!isset($userData[$userId]['first_name'])) {
        $userData[$userId]['first_name'] = null;
    }
    if (!isset($userData[$userId]['last_name'])) {
        $userData[$userId]['last_name'] = null;
    }
    if (!isset($userData[$userId]['language'])) {
        $userData[$userId]['language'] = 'ru';
    }
    if (!isset($userData[$userId]['registered_at'])) {
        $userData[$userId]['registered_at'] = time();
    }
    if (!isset($userData[$userId]['last_activity'])) {
        $userData[$userId]['last_activity'] = time();
    }
    if (!isset($userData[$userId]['withdrawal_log'])) {
        $userData[$userId]['withdrawal_log'] = [];
    }
    if (!isset($userData[$userId]['withdrawals_count'])) {
        $userData[$userId]['withdrawals_count'] = count($userData[$userId]['withdrawals'] ?? []);
    }
    if (!isset($userData[$userId]['suspicious_activity_count'])) {
        $userData[$userId]['suspicious_activity_count'] = 0;
    }

    // Приводим типы данных к правильным
    $userData[$userId]['id'] = intval($userData[$userId]['id']);
    $userData[$userId]['balance'] = intval($userData[$userId]['balance']);
    $userData[$userId]['total_earned'] = intval($userData[$userId]['total_earned']);
    $userData[$userId]['referrals_count'] = count($userData[$userId]['referrals']);
    $userData[$userId]['referral_earnings'] = intval($userData[$userId]['referral_earnings']);
    $userData[$userId]['withdrawals_count'] = count($userData[$userId]['withdrawals']);
    $userData[$userId]['suspicious_activity'] = intval($userData[$userId]['suspicious_activity']);
    $userData[$userId]['suspicious_activity_count'] = intval($userData[$userId]['suspicious_activity_count']);
    $userData[$userId]['blocked'] = (bool)$userData[$userId]['blocked'];

    if ($userData[$userId]['referrer_id'] !== null) {
        $userData[$userId]['referrer_id'] = intval($userData[$userId]['referrer_id']);
    }

    // Убеждаемся что referrals содержит только числа
    if (!empty($userData[$userId]['referrals'])) {
        $userData[$userId]['referrals'] = array_map('intval', array_filter($userData[$userId]['referrals'], 'is_numeric'));
        $userData[$userId]['referrals_count'] = count($userData[$userId]['referrals']);
    }

    return [
        'balance' => $userData[$userId]['balance'] ?? 0,
        'referrer_id' => $userData[$userId]['referrer_id'] ?? null,
        'referrals_count' => $userData[$userId]['referrals_count'] ?? 0,
        'referral_earnings' => $userData[$userId]['referral_earnings'] ?? 0,
        'withdrawals_count' => count($userData[$userId]['withdrawals'] ?? []),
        'username' => $userData[$userId]['username'],
        'first_name' => $userData[$userId]['first_name'],
        'last_name' => $userData[$userId]['last_name']
    ];
}
function increaseUserBalance(int $userId, int $amount, array &$userData): int|false { if ($amount <= 0) { return false; } if (!isset($userData[$userId]) || !array_key_exists('balance',$userData[$userId])) { error_log("db_mock ERROR: Increase balance: user $userId not found or no balance key."); return false; } $currentBalance = $userData[$userId]['balance']; if (!is_numeric($currentBalance)){ $currentBalance = 0; error_log("db_mock WARNING: Non-numeric balance for $userId, resetting to 0.");} if ((PHP_INT_MAX - $amount) < $currentBalance) { error_log("db_mock ERROR: Integer overflow for user $userId."); return false; } $newBalance = $currentBalance + $amount; $userData[$userId]['balance'] = $newBalance; return $newBalance; }
function getUserReferrerId(int $userId, array $userData): ?int { if (!isset($userData[$userId])) { return null; } return $userData[$userId]['referrer_id'] ?? null; }

/**
 * Автоматически исправляет и синхронизирует реферальные связи
 *
 * @param array &$userData Массив данных всех пользователей
 * @return bool Были ли внесены изменения
 */
function fixReferralLinks(array &$userData): bool {
    $fixed = false;

    // Проходим по всем пользователям
    foreach ($userData as $userId => &$user) {
        // Проверяем что у пользователя с реферером есть обратная связь
        if (!empty($user['referrer_id'])) {
            $referrerId = $user['referrer_id'];

            if (isset($userData[$referrerId])) {
                // Убеждаемся что у реферера есть массив рефералов
                if (!isset($userData[$referrerId]['referrals'])) {
                    $userData[$referrerId]['referrals'] = [];
                    $fixed = true;
                }

                // Добавляем пользователя в список рефералов реферера (если его там нет)
                if (!in_array($userId, $userData[$referrerId]['referrals'])) {
                    $userData[$referrerId]['referrals'][] = intval($userId);
                    $userData[$referrerId]['referrals_count'] = count($userData[$referrerId]['referrals']);
                    error_log("fixReferralLinks: Добавлен пользователь $userId в список рефералов реферера $referrerId");
                    $fixed = true;
                }
            } else {
                // Реферер не существует, убираем ссылку
                error_log("fixReferralLinks: Убираем несуществующего реферера $referrerId у пользователя $userId");
                $user['referrer_id'] = null;
                $fixed = true;
            }
        }

        // Проверяем что все рефералы в списке существуют и ссылаются обратно
        if (!empty($user['referrals'])) {
            $validReferrals = [];

            foreach ($user['referrals'] as $referralId) {
                $referralId = intval($referralId);

                if (isset($userData[$referralId])) {
                    if ($userData[$referralId]['referrer_id'] == $userId) {
                        $validReferrals[] = $referralId;
                    } else {
                        // Исправляем реферера у реферала
                        error_log("fixReferralLinks: Исправляем реферера для пользователя $referralId: " .
                                 ($userData[$referralId]['referrer_id'] ?? 'null') . " → $userId");
                        $userData[$referralId]['referrer_id'] = intval($userId);
                        $validReferrals[] = $referralId;
                        $fixed = true;
                    }
                } else {
                    // Реферал не существует, удаляем из списка
                    error_log("fixReferralLinks: Удаляем несуществующего реферала $referralId из списка пользователя $userId");
                    $fixed = true;
                }
            }

            // Обновляем список рефералов
            if (count($validReferrals) != count($user['referrals'])) {
                $user['referrals'] = $validReferrals;
                $user['referrals_count'] = count($validReferrals);
                $fixed = true;
            }
        }

        // Убеждаемся что referrals_count соответствует реальному количеству
        $actualCount = count($user['referrals'] ?? []);
        if (($user['referrals_count'] ?? 0) != $actualCount) {
            $user['referrals_count'] = $actualCount;
            $fixed = true;
        }
    }

    if ($fixed) {
        error_log("fixReferralLinks: Реферальные связи исправлены");
    }

    return $fixed;
}

?>