<?php
/**
 * api/auto_update_withdrawals.php
 * Скрипт для автоматического обновления статусов выплат через NOWPayments API
 * Рекомендуется запускать через cron каждые 5-10 минут
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) {
    error_log('FATAL: config.php not found in auto_update_withdrawals.php');
    exit;
}
if (!(@require_once __DIR__ . '/db_mock.php')) {
    error_log('FATAL: db_mock.php not found in auto_update_withdrawals.php');
    exit;
}
if (!(@require_once __DIR__ . '/NOWPaymentsAPI.php')) {
    error_log('FATAL: NOWPaymentsAPI.php not found in auto_update_withdrawals.php');
    exit;
}
// --- Конец проверки зависимостей ---

// Создаем экземпляр API клиента
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Загружаем данные пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    error_log("auto_update_withdrawals ERROR: Не удалось загрузить данные пользователей");
    exit;
}

$updatedCount = 0;

// Проходим по всем пользователям и их выплатам
foreach ($userData as $userId => &$user) {
    if (empty($user['withdrawals']) || !is_array($user['withdrawals'])) {
        continue;
    }

    foreach ($user['withdrawals'] as &$withdrawal) {
        // Пропускаем выплаты с финальными статусами
        $status = strtolower($withdrawal['status'] ?? '');
        if (in_array($status, ['confirmed', 'completed', 'success', 'failed', 'error', 'cancelled'])) {
            continue;
        }

        // Проверяем наличие ID выплаты в NOWPayments
        $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
        if (empty($payoutId)) {
            error_log("auto_update_withdrawals WARNING: Пропуск выплаты без ID у пользователя $userId");
            continue;
        }

        try {
            // Получаем статус выплаты из API
            $payoutStatus = $api->getPayoutStatus($payoutId);
            
            if (isset($payoutStatus['payout_status'])) {
                $newStatus = strtolower($payoutStatus['payout_status']);
                
                // Нормализуем статусы NOWPayments
                $statusMap = [
                    'waiting' => 'pending',
                    'confirming' => 'confirming',
                    'sending' => 'sending',
                    'finished' => 'confirmed',
                    'failed' => 'failed',
                    'refunded' => 'refunded',
                    'expired' => 'expired'
                ];
                
                $newStatus = $statusMap[$newStatus] ?? $newStatus;
                
                // Обновляем статус только если он изменился
                if ($newStatus !== $status) {
                    $withdrawal['status'] = $newStatus;
                    $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                    
                    // Сохраняем дополнительные данные из API
                    if (isset($payoutStatus['txid'])) $withdrawal['txid'] = $payoutStatus['txid'];
                    if (isset($payoutStatus['payout_amount'])) $withdrawal['crypto_amount'] = $payoutStatus['payout_amount'];
                    
                    $updatedCount++;
                    error_log("auto_update_withdrawals INFO: Обновлен статус выплаты $payoutId: $status => $newStatus");
                }
            }
        } catch (Exception $e) {
            error_log("auto_update_withdrawals ERROR: Ошибка при проверке выплаты $payoutId: " . $e->getMessage());
        }
    }
}

// Сохраняем обновленные данные
if ($updatedCount > 0) {
    if (saveUserData($userData)) {
        error_log("auto_update_withdrawals SUCCESS: Обновлено $updatedCount выплат");
    } else {
        error_log("auto_update_withdrawals ERROR: Не удалось сохранить данные после обновления $updatedCount выплат");
    }
} else {
    error_log("auto_update_withdrawals INFO: Нет выплат для обновления");
}
?>
