<?php
/**
 * test_images.php
 * Тестирование изображений бота и обновление кэша
 */

require_once 'config.php';

echo "<h2>🖼️ Тестирование изображений бота</h2>\n";

// Список изображений для проверки
$images = [
    'bot_welcome_super_banner.png' => 'Приветственный баннер',
    'bot_avatar.svg' => 'Аватарка бота (SVG)',
    'bot_logo.svg' => 'Логотип бота',
    'bot_preview.svg' => 'Превью бота'
];

echo "<h3>📋 Проверка доступности изображений:</h3>\n";

foreach ($images as $filename => $description) {
    $localPath = __DIR__ . '/../images/' . $filename;
    $webUrl = 'https://app.uniqpaid.com/test3/images/' . $filename;
    
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>\n";
    echo "<h4>🖼️ {$description} ({$filename})</h4>\n";
    
    // Проверяем локальный файл
    if (file_exists($localPath)) {
        $fileSize = filesize($localPath);
        $fileTime = date('Y-m-d H:i:s', filemtime($localPath));
        echo "<p style='color: green;'>✅ Локальный файл существует</p>\n";
        echo "<p>📁 Размер: " . number_format($fileSize) . " байт</p>\n";
        echo "<p>🕒 Изменен: {$fileTime}</p>\n";
        echo "<p>📍 Путь: {$localPath}</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Локальный файл не найден: {$localPath}</p>\n";
    }
    
    // Проверяем веб-доступность
    echo "<p>🌐 Web URL: <a href='{$webUrl}' target='_blank'>{$webUrl}</a></p>\n";
    
    // Для PNG файлов показываем превью
    if (strpos($filename, '.png') !== false && file_exists($localPath)) {
        echo "<p>🖼️ Превью:</p>\n";
        echo "<img src='{$webUrl}?v=" . time() . "' style='max-width: 300px; max-height: 200px; border: 1px solid #ccc;' alt='{$description}'>\n";
    }
    
    echo "</div>\n";
}

echo "<hr>\n";

echo "<h3>🔄 Обновление webhook с новыми изображениями:</h3>\n";

// Тестируем отправку изображения через Telegram API
$testChatId = '123456789'; // Замените на ваш chat_id для тестирования

echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<h4>📤 Тест отправки изображения через Telegram API</h4>\n";

$timestamp = time();
$logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?v=' . $timestamp;

echo "<p>🔗 URL с timestamp: {$logoUrl}</p>\n";
echo "<p>⏰ Timestamp: {$timestamp}</p>\n";

// Проверяем доступность URL
$headers = @get_headers($logoUrl);
if ($headers && strpos($headers[0], '200') !== false) {
    echo "<p style='color: green;'>✅ URL доступен (HTTP 200)</p>\n";
} else {
    echo "<p style='color: red;'>❌ URL недоступен</p>\n";
    echo "<p>Заголовки: " . print_r($headers, true) . "</p>\n";
}

echo "</div>\n";

echo "<hr>\n";

echo "<h3>🛠️ Рекомендации для решения проблем с изображениями:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Кэш Telegram:</strong> Telegram кэширует изображения. Используйте параметр ?v=timestamp для обхода кэша</li>\n";
echo "<li><strong>Формат файлов:</strong> Telegram Bot API не поддерживает SVG. Используйте PNG/JPG</li>\n";
echo "<li><strong>Размер файлов:</strong> Максимальный размер файла для Telegram - 50MB</li>\n";
echo "<li><strong>HTTPS:</strong> Убедитесь, что ваш домен доступен по HTTPS</li>\n";
echo "<li><strong>Права доступа:</strong> Проверьте права доступа к файлам изображений</li>\n";
echo "</ol>\n";

echo "<hr>\n";

echo "<h3>🔧 Действия для обновления:</h3>\n";
echo "<ol>\n";
echo "<li><a href='update_webhook.php'>🔄 Обновить webhook</a></li>\n";
echo "<li><a href='../admin/cache.php'>🗑️ Очистить кэш приложения</a></li>\n";
echo "<li>📱 Перезапустить бота в Telegram</li>\n";
echo "<li>🔄 Отправить команду /start боту заново</li>\n";
echo "</ol>\n";

echo "<hr>\n";
echo "<p><a href='../'>🏠 Вернуться к приложению</a></p>\n";
echo "<p><a href='../admin/'>⚙️ Админ панель</a></p>\n";
?>
