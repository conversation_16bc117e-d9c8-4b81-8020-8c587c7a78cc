<?php
/**
 * Проверка статуса выплаты NOWPayments
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

$payoutId = $_GET['id'] ?? null;

if (!$payoutId) {
    echo "<h1>❌ Ошибка</h1>\n";
    echo "<p>Не указан ID выплаты</p>\n";
    echo "<p><a href='test_real_payout.php'>← Вернуться к тестированию</a></p>\n";
    exit;
}

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "<h1>🔍 Проверка статуса выплаты</h1>\n";
echo "<p><strong>ID выплаты:</strong> {$payoutId}</p>\n";

// Получаем статус выплаты
$status = $api->getPayoutStatus($payoutId);

if ($status) {
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>\n";
    echo "<strong>✅ Статус получен успешно</strong><br><br>\n";
    
    // Показываем основную информацию
    if (isset($status['status'])) {
        $statusText = $status['status'];
        $statusColor = '#007bff';
        
        switch (strtolower($statusText)) {
            case 'pending':
                $statusColor = '#ffc107';
                $statusText = '⏳ В ожидании';
                break;
            case 'processing':
                $statusColor = '#17a2b8';
                $statusText = '⚙️ Обрабатывается';
                break;
            case 'finished':
            case 'completed':
                $statusColor = '#28a745';
                $statusText = '✅ Завершена';
                break;
            case 'failed':
            case 'error':
                $statusColor = '#dc3545';
                $statusText = '❌ Ошибка';
                break;
        }
        
        echo "<div style='background: {$statusColor}; color: white; padding: 10px; border-radius: 5px; margin-bottom: 10px;'>\n";
        echo "<strong>Статус:</strong> {$statusText}\n";
        echo "</div>\n";
    }
    
    // Показываем детали
    if (isset($status['amount'])) {
        echo "<strong>💰 Сумма:</strong> {$status['amount']} " . ($status['currency'] ?? '') . "<br>\n";
    }
    
    if (isset($status['address'])) {
        echo "<strong>📍 Адрес:</strong> {$status['address']}<br>\n";
    }
    
    if (isset($status['txid'])) {
        echo "<strong>🔗 Transaction ID:</strong> {$status['txid']}<br>\n";
    }
    
    if (isset($status['created_at'])) {
        echo "<strong>📅 Создана:</strong> {$status['created_at']}<br>\n";
    }
    
    if (isset($status['updated_at'])) {
        echo "<strong>🔄 Обновлена:</strong> {$status['updated_at']}<br>\n";
    }
    
    echo "<br><strong>📄 Полный ответ API:</strong><br>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    echo json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>\n";
    echo "</div>\n";
    
} else {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 5px;'>\n";
    echo "<strong>❌ Не удалось получить статус выплаты</strong><br>\n";
    echo "Возможные причины:<br>\n";
    echo "- Неверный ID выплаты<br>\n";
    echo "- Проблемы с API ключами<br>\n";
    echo "- Выплата еще не обработана системой<br>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<h2>🔗 Полезные ссылки</h2>\n";
echo "<p>\n";
echo "<a href='test_real_payout.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Новый тест</a>\n";
echo "<a href='https://account.nowpayments.io/payouts' target='_blank' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Панель NOWPayments</a>\n";
echo "<a href='test_fee_handling.php' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;'>🔧 Тест функций</a>\n";
echo "</p>\n";

// Автообновление страницы для отслеживания статуса
if (isset($status['status']) && in_array(strtolower($status['status']), ['pending', 'processing'])) {
    echo "<script>\n";
    echo "setTimeout(function() { window.location.reload(); }, 30000);\n"; // Обновление каждые 30 секунд
    echo "</script>\n";
    echo "<p><small>🔄 Страница автоматически обновится через 30 секунд для проверки изменений статуса</small></p>\n";
}
?>
