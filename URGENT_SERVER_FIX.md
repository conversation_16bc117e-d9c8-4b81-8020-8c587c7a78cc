# 🚨 СРОЧНОЕ ИСПРАВЛЕНИЕ СЕРВЕРА

## ❌ Текущие проблемы:
1. `X-Frame-Options: deny` - блокирует iframe
2. 404 ошибки - файлы не найдены
3. WebSocket ошибки Telegram

## ✅ НЕМЕДЛЕННЫЕ ДЕЙСТВИЯ:

### 1. Проверить и исправить .htaccess на сервере

**Проблема**: Изменения в .htaccess не применились на `app.uniqpaid.com`

**Решение**: Заменить содержимое основного .htaccess файла:

```apache
# Главный .htaccess для UniQPaid - ИСПРАВЛЕННАЯ ВЕРСИЯ
# РАЗРЕШАЕМ IFRAME ДЛЯ TELEGRAM

RewriteEngine On

# КРИТИЧЕСКИ ВАЖНО: Разрешаем отображение в iframe для Telegram
<IfModule mod_headers.c>
    # Убираем блокирующий заголовок
    Header always unset X-Frame-Options
    
    # Разрешаем iframe для всех (временно для диагностики)
    Header always set X-Frame-Options "ALLOWALL"
    
    # Альтернативный способ через CSP
    Header always set Content-Security-Policy "frame-ancestors *; default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; img-src 'self' data: https:; font-src 'self' data: https:;"
    
    # CORS для API
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    
    # Базовая безопасность (но не блокирующая iframe)
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Обработка preflight запросов
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Защита системных файлов
<Files ".htaccess">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.backup*">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем просмотр директорий
Options -Indexes

# Разрешаем основные файлы приложения
<Files "index.html">
    Order Allow,Deny
    Allow from all
</Files>

<Files "main.js">
    Order Allow,Deny
    Allow from all
</Files>

<Files "cyberpunk-styles.css">
    Order Allow,Deny
    Allow from all
</Files>

# Кэширование
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
</IfModule>

# Сжатие
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>
```

### 2. Проверить файлы на сервере

**Убедиться что существуют:**
- ✅ `/index.html`
- ✅ `/main.js`
- ✅ `/cyberpunk-styles.css`
- ✅ `/api/getUserData.php`
- ✅ `/api/getWithdrawalHistory.php`
- ✅ `/images/` (папка с изображениями)
- ✅ `/js/` (папка с JS файлами)
- ✅ `/locales/` (папка с переводами)

### 3. Быстрая диагностика

**Команды для проверки на сервере:**

```bash
# Проверить заголовки
curl -I https://app.uniqpaid.com/

# Проверить основные файлы
curl -s -o /dev/null -w "%{http_code}" https://app.uniqpaid.com/index.html
curl -s -o /dev/null -w "%{http_code}" https://app.uniqpaid.com/main.js
curl -s -o /dev/null -w "%{http_code}" https://app.uniqpaid.com/cyberpunk-styles.css

# Проверить API
curl -s -o /dev/null -w "%{http_code}" https://app.uniqpaid.com/api/getUserData.php
```

### 4. Альтернативное решение (если .htaccess не работает)

**Добавить в начало index.html:**

```html
<script>
// Принудительно убираем X-Frame-Options через meta
document.addEventListener('DOMContentLoaded', function() {
    // Удаляем все мета-теги с X-Frame-Options
    const metaTags = document.querySelectorAll('meta[http-equiv="X-Frame-Options"]');
    metaTags.forEach(tag => tag.remove());
    
    // Добавляем разрешающий мета-тег
    const meta = document.createElement('meta');
    meta.httpEquiv = 'X-Frame-Options';
    meta.content = 'ALLOWALL';
    document.head.appendChild(meta);
});
</script>
```

### 5. Проверка через PHP

**Создать файл test-iframe.php на сервере:**

```php
<?php
header('X-Frame-Options: ALLOWALL');
header('Content-Security-Policy: frame-ancestors *');
header('Access-Control-Allow-Origin: *');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Тест iframe</title>
</head>
<body>
    <h1>✅ Если видите это в Telegram - iframe работает!</h1>
    <p>Время: <?php echo date('Y-m-d H:i:s'); ?></p>
    <script>
        if (window.self !== window.top) {
            document.body.style.background = 'green';
            document.body.innerHTML += '<h2>🎉 IFRAME РАБОТАЕТ!</h2>';
        }
    </script>
</body>
</html>
```

## 🔥 КРИТИЧЕСКИЕ ШАГИ:

### Шаг 1: Немедленно
1. Зайти на сервер `app.uniqpaid.com`
2. Заменить `.htaccess` файл новым содержимым
3. Перезапустить Apache: `sudo systemctl restart apache2`

### Шаг 2: Проверить
1. Открыть: `https://app.uniqpaid.com/`
2. Проверить в DevTools заголовки ответа
3. Должно быть: `X-Frame-Options: ALLOWALL`

### Шаг 3: Тестировать
1. Открыть @uniqpaid_paid_bot
2. Нажать "Запустить приложение"
3. Проверить что загружается без ошибок

## ⚡ ЕСЛИ НЕ ПОМОГАЕТ:

### Вариант A: Временно убрать все ограничения
```apache
# Минимальный .htaccess - только разрешения
<IfModule mod_headers.c>
    Header always unset X-Frame-Options
    Header always set Access-Control-Allow-Origin "*"
</IfModule>
Options -Indexes
```

### Вариант B: Проверить настройки хостинга
- Включен ли mod_headers?
- Есть ли ограничения на .htaccess?
- Работает ли Apache правильно?

## 📞 ПОДДЕРЖКА:
Если ничего не помогает - проверить:
1. Логи Apache: `/var/log/apache2/error.log`
2. Настройки хостинга
3. Связаться с хостинг-провайдером

---
**СТАТУС**: 🚨 ТРЕБУЕТ НЕМЕДЛЕННОГО ИСПРАВЛЕНИЯ
