<?php
/**
 * webhook.php
 * Основной файл обработки webhook от Telegram
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../api/db_mock.php';

// Пробуем подключить локализацию, но не падаем если её нет
$localizationAvailable = false;
if (file_exists(__DIR__ . '/../includes/Localization.php')) {
    try {
        require_once __DIR__ . '/../includes/Localization.php';
        $localizationAvailable = true;
    } catch (Exception $e) {
        botLog("WARNING: Локализация недоступна: " . $e->getMessage());
    }
}

/**
 * Безопасное получение перевода
 */
function getTranslation($key, $params = [], $fallback = null, $userLanguage = 'ru') {
    global $localizationAvailable;

    // Сначала пробуем загрузить переводы напрямую из файлов
    $localeFile = __DIR__ . '/../locales/' . $userLanguage . '.json';
    if (file_exists($localeFile)) {
        try {
            $translations = json_decode(file_get_contents($localeFile), true);
            if ($translations && isset($translations['bot'])) {
                $translation = getNestedValue($translations, $key);
                if ($translation) {
                    // Заменяем параметры
                    foreach ($params as $param => $value) {
                        $translation = str_replace('{' . $param . '}', $value, $translation);
                    }
                    return $translation;
                }
            }
        } catch (Exception $e) {
            botLog("WARNING: Ошибка чтения локализации: " . $e->getMessage());
        }
    }

    // Fallback на класс локализации если доступен
    if ($localizationAvailable) {
        try {
            $localization = Localization::getInstance();
            $localization->setLanguage($userLanguage);
            $translation = $localization->get($key, $params);

            // Если перевод найден и это не ключ
            if ($translation !== $key) {
                return $translation;
            }
        } catch (Exception $e) {
            botLog("WARNING: Ошибка локализации: " . $e->getMessage());
        }
    }

    // Fallback на переданный текст
    if ($fallback !== null) {
        // Заменяем параметры в fallback тексте
        foreach ($params as $param => $value) {
            $fallback = str_replace('{' . $param . '}', $value, $fallback);
        }
        return $fallback;
    }

    return $key;
}

/**
 * Получение вложенного значения из массива по ключу с точками
 */
function getNestedValue($array, $key) {
    $keys = explode('.', $key);
    $value = $array;

    foreach ($keys as $k) {
        if (isset($value[$k])) {
            $value = $value[$k];
        } else {
            return null;
        }
    }

    return is_string($value) ? $value : null;
}

// Получаем данные от Telegram
$input = file_get_contents('php://input');
$update = json_decode($input, true);

if (!$update) {
    botLog("ERROR: Не удалось декодировать JSON от Telegram");
    exit;
}

botLog("INFO: Получен update: " . json_encode($update));

// Обрабатываем сообщение
if (isset($update['message'])) {
    handleMessage($update['message']);
}

// Обрабатываем callback query (нажатие на inline кнопки)
if (isset($update['callback_query'])) {
    handleCallbackQuery($update['callback_query']);
}

/**
 * Обработка текстовых сообщений
 */
function handleMessage($message) {
    $chatId = $message['chat']['id'];
    $userId = $message['from']['id'];
    $text = $message['text'] ?? '';
    $firstName = $message['from']['first_name'] ?? 'Пользователь';
    $lastName = $message['from']['last_name'] ?? '';
    $username = $message['from']['username'] ?? '';

    botLog("INFO: Сообщение от пользователя {$userId} ({$firstName}): {$text}");

    // Команда /start
    if (strpos($text, '/start') === 0) {
        handleStartCommand($chatId, $userId, $firstName, $lastName, $username, $text, $message);
        return;
    }

    // Команда /help
    if ($text === '/help') {
        handleHelpCommand($chatId);
        return;
    }

    // Команда /balance
    if ($text === '/balance') {
        handleBalanceCommand($chatId, $userId);
        return;
    }

    // Команда /stats
    if ($text === '/stats') {
        handleStatsCommand($chatId, $userId);
        return;
    }

    // По умолчанию показываем главное меню
    showMainMenu($chatId, $firstName);
}

/**
 * Обработка команды /start
 */
function handleStartCommand($chatId, $userId, $firstName, $lastName, $username, $text, $message) {
    // Проверяем, есть ли реферальный код
    $referrerId = null;
    if (preg_match('/\/start\s+(\d+)/', $text, $matches)) {
        $referrerId = (int)$matches[1];
        botLog("INFO: Пользователь {$userId} пришел по реферальной ссылке от {$referrerId}");
    }

    // Определяем язык пользователя
    $userLanguage = 'en'; // По умолчанию английский
    if (isset($message['from']['language_code'])) {
        $langCode = strtolower($message['from']['language_code']);
        botLog("INFO: Определение языка для пользователя {$userId}: language_code = {$langCode}");

        // Если язык входит в список русскоязычных - ставим русский
        if (in_array($langCode, ['ru', 'be', 'uk', 'kk', 'ky', 'tg', 'uz', 'hy', 'az', 'ka', 'ro'])) {
            $userLanguage = 'ru';
        }

        botLog("INFO: Установлен язык для пользователя {$userId}: {$userLanguage}");
    } else {
        botLog("INFO: language_code не найден для пользователя {$userId}, используем английский по умолчанию");
    }

    // Регистрируем или обновляем пользователя
    registerUser($userId, $firstName, $lastName, $username, $referrerId, $userLanguage);

    // Создаем приветственное сообщение с fallback текстами
    $welcomeText = getTranslation('bot.welcome_title', [], "🎉 Добро пожаловать в UniQPaid!", $userLanguage) . "\n\n";
    $welcomeText .= "<b>" . getTranslation('bot.welcome_subtitle', [], "💰 Смотри рекламу сейчас и получай крипту сразу!", $userLanguage) . "</b>\n";
    $welcomeText .= "<b>" . getTranslation('bot.welcome_description', [], "🚀 Не нужно ждать листинга! Моментальные автовыплаты на кошелёк!", $userLanguage) . "</b>\n\n";

    // Добавляем информацию о регулярности проверки рекламы
    $welcomeText .= getTranslation('bot.ad_availability_info', [], "📊 Регулярно проверяйте приложение на наличие новых рекламных предложений. В день доступно примерно 20 рекламных баннеров и до 50 видеореклам.", $userLanguage) . "\n\n";

    // Добавляем предупреждение о размере выводов
    $welcomeText .= getTranslation('bot.withdrawal_warning', [], "⚠️ Будьте внимательны к размеру выводимой суммы. Слишком малые выплаты могут зависнуть из-за минимальных лимитов бирж и обменных сервисов.", $userLanguage) . "\n\n";

    $welcomeText .= "<b>" . getTranslation('bot.how_it_works', [], "📊 Как это работает:", $userLanguage) . "</b>\n";
    $welcomeText .= getTranslation('bot.earn_coins', ['coins' => COINS_PER_VIEW], "• Смотрите рекламу и получайте " . COINS_PER_VIEW . " монет", $userLanguage) . "\n";
    $welcomeText .= getTranslation('bot.invite_friends', ['percent' => REFERRAL_BONUS_PERCENT], "• Приглашайте друзей и получайте " . REFERRAL_BONUS_PERCENT . "% от их заработка", $userLanguage) . "\n";
    $welcomeText .= getTranslation('bot.withdraw_crypto', [], "• Выводите заработанные средства на криптокошельки", $userLanguage) . "\n\n";
    // Правильно форматируем курс
    $formattedRate = COIN_VALUE_USD == floor(COIN_VALUE_USD) ? number_format(COIN_VALUE_USD, 0) : rtrim(rtrim(number_format(COIN_VALUE_USD, 3), '0'), '.');
    $welcomeText .= getTranslation('bot.exchange_rate', ['rate' => $formattedRate], "💎 Курс: 1 монета = $" . $formattedRate . " USD", $userLanguage) . "\n\n";
    $welcomeText .= "<b>" . getTranslation('bot.start_earning', [], "🚀 Начните зарабатывать прямо сейчас!", $userLanguage) . "</b>";

    // Кнопки с fallback текстами
    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getTranslation('bot.launch_app', [], "🚀 Запустить приложение", $userLanguage),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getTranslation('bot.friends', [], "👥 Друзья", $userLanguage),
                    'callback_data' => 'invite_friends'
                ],
                [
                    'text' => getTranslation('bot.my_balance', [], "💰 Мой баланс", $userLanguage),
                    'callback_data' => 'my_balance'
                ]
            ],
            [
                [
                    'text' => getTranslation('bot.statistics', [], "📊 Статистика", $userLanguage),
                    'callback_data' => 'my_stats'
                ],
                [
                    'text' => getTranslation('bot.help', [], "❓ Помощь", $userLanguage),
                    'callback_data' => 'help'
                ]
            ]
        ]
    ];

    // Красивый баннер для приветственного сообщения в кибер-панк стиле
    // ВАЖНО: Telegram Bot API НЕ поддерживает SVG! Используем PNG версию
    // Добавляем timestamp для обхода кэша Telegram
    $timestamp = time();
    $logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?v=' . $timestamp;

    // Пробуем отправить с логотипом
    $result = sendPhoto($chatId, $logoUrl, $welcomeText, $keyboard);

    if (!$result) {
        // Если логотип не отправился, отправляем красивое текстовое сообщение
        botLog("WARNING: Не удалось отправить картинку, отправляем текст с эмодзи");

        // Добавляем красивый заголовок с эмодзи
        $welcomeHeader = "🎨🚀💎 <b>UniQPaid - Криптозаработок</b> 💎🚀🎨\n\n";
        $fullWelcomeText = $welcomeHeader . $welcomeText;

        sendMessage($chatId, $fullWelcomeText, $keyboard);
    } else {
        botLog("INFO: Картинка успешно отправлена пользователю {$userId}");
    }
}

/**
 * Регистрация или обновление пользователя
 */
function registerUser($userId, $firstName, $lastName, $username, $referrerId = null, $userLanguage = 'ru') {
    $userData = loadUserData();
    $isNewUser = !isset($userData[$userId]);

    if ($isNewUser) {
        // Новый пользователь
        $userData[$userId] = [
            'balance' => 0,
            'total_earned' => 0,
            'withdrawals' => [],
            'withdrawal_log' => [],
            'referrer_id' => $referrerId,
            'referrals' => [],
            'referral_earnings' => 0,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'username' => $username,
            'language' => $userLanguage,
            'registered_at' => time(),
            'last_activity' => time(),
            'suspicious_activity_count' => 0,
            'withdrawals_count' => 0
        ];

        botLog("INFO: Зарегистрирован новый пользователь {$userId} ({$firstName})");

        // Если есть реферер, добавляем к нему реферала
        if ($referrerId && isset($userData[$referrerId])) {
            if (!isset($userData[$referrerId]['referrals'])) {
                $userData[$referrerId]['referrals'] = [];
            }

            $userData[$referrerId]['referrals'][] = [
                'user_id' => $userId,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'username' => $username,
                'registered_at' => time()
            ];

            botLog("INFO: Пользователь {$userId} добавлен как реферал к {$referrerId}");
        }
    } else {
        // Обновляем данные существующего пользователя
        $userData[$userId]['first_name'] = $firstName;
        $userData[$userId]['last_name'] = $lastName;
        $userData[$userId]['username'] = $username;
        $userData[$userId]['last_activity'] = time();
    }

    saveUserData($userData);
    return $isNewUser;
}

/**
 * Получение языка пользователя
 */
function getUserLanguage($userId) {
    $userData = loadUserData();

    if (isset($userData[$userId])) {
        $language = $userData[$userId]['language'] ?? 'en';
        botLog("INFO: Получен язык для пользователя {$userId}: {$language}");
        return $language;
    }

    botLog("INFO: Пользователь {$userId} не найден, используем английский по умолчанию");
    return 'en';
}

/**
 * Получение имени пользователя
 */
function getUserFirstName($userId) {
    $userData = loadUserData();

    if (isset($userData[$userId])) {
        return $userData[$userId]['first_name'] ?? 'Пользователь';
    }

    return 'Пользователь';
}

/**
 * Показ главного меню
 */
function showMainMenu($chatId, $firstName, $userId = null) {
    // Получаем язык пользователя
    $userLanguage = 'en'; // По умолчанию английский
    if ($userId) {
        $userLanguage = getUserLanguage($userId);
    }

    $text = getTranslation('bot.hello', ['name' => $firstName], "👋 Привет, {$firstName}!", $userLanguage) . "\n\n";
    $text .= getTranslation('bot.choose_action', [], "Выберите действие:", $userLanguage);

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getTranslation('bot.open_app', [], "🚀 Открыть приложение", $userLanguage),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getTranslation('bot.balance', [], "💰 Баланс", $userLanguage),
                    'callback_data' => 'my_balance'
                ],
                [
                    'text' => getTranslation('bot.friends', [], "👥 Друзья", $userLanguage),
                    'callback_data' => 'invite_friends'
                ]
            ],
            [
                [
                    'text' => getTranslation('bot.statistics', [], "📊 Статистика", $userLanguage),
                    'callback_data' => 'my_stats'
                ],
                [
                    'text' => getTranslation('bot.help', [], "❓ Помощь", $userLanguage),
                    'callback_data' => 'help'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка callback query (нажатие на inline кнопки)
 */
function handleCallbackQuery($callbackQuery) {
    $chatId = $callbackQuery['message']['chat']['id'];
    $userId = $callbackQuery['from']['id'];
    $data = $callbackQuery['data'];
    $messageId = $callbackQuery['message']['message_id'];

    botLog("INFO: Callback query от {$userId}: {$data}");

    // Подтверждаем получение callback
    telegramRequest('answerCallbackQuery', ['callback_query_id' => $callbackQuery['id']]);

    switch ($data) {
        case 'my_balance':
            handleBalanceCommand($chatId, $userId);
            break;

        case 'invite_friends':
            handleInviteFriends($chatId, $userId);
            break;

        case 'my_stats':
            handleStatsCommand($chatId, $userId);
            break;

        case 'help':
            handleHelpCommand($chatId);
            break;

        case 'main_menu':
            $firstName = getUserFirstName($userId);
            showMainMenu($chatId, $firstName, $userId);
            break;

        default:
            sendMessage($chatId, "Неизвестная команда.");
    }
}

/**
 * Обработка команды баланса
 */
function handleBalanceCommand($chatId, $userId) {
    $userData = loadUserData();
    $userLanguage = getUserLanguage($userId);

    if (!isset($userData[$userId])) {
        sendMessage($chatId, getTranslation('bot.user_not_found', [], "❌ Пользователь не найден. Отправьте /start для регистрации.", $userLanguage));
        return;
    }

    $user = $userData[$userId];
    $balance = $user['balance'] ?? 0;
    $totalEarned = $user['total_earned'] ?? 0;
    $referralEarnings = $user['referral_earnings'] ?? 0;

    $text = getTranslation('bot.your_balance', [], "💰 Ваш баланс", $userLanguage) . "\n\n";
    $text .= getTranslation('bot.current_balance', ['balance' => $balance], "🪙 Текущий баланс: {$balance} монет", $userLanguage) . "\n";
    // Правильно форматируем доллары - убираем лишние нули
    $usdAmount = $balance * COIN_VALUE_USD;
    $formattedUsd = $usdAmount == floor($usdAmount) ? number_format($usdAmount, 0) : rtrim(rtrim(number_format($usdAmount, 3), '0'), '.');
    $text .= getTranslation('bot.balance_usd', ['amount' => $formattedUsd], "💵 В долларах: $" . $formattedUsd . " USD", $userLanguage) . "\n\n";
    $text .= getTranslation('bot.total_earned', ['amount' => $totalEarned], "📈 Всего заработано: {$totalEarned} монет", $userLanguage) . "\n";
    $text .= getTranslation('bot.from_referrals', ['amount' => $referralEarnings], "👥 От рефералов: {$referralEarnings} монет", $userLanguage) . "\n";
    $text .= getTranslation('bot.withdrawals_count', ['count' => $user['withdrawals_count'] ?? 0], "💸 Выводов: " . ($user['withdrawals_count'] ?? 0), $userLanguage) . "\n\n";
    
    // Показываем последние 3 выплаты
    if (!empty($user['withdrawals'])) {
        $text .= "📜 <b>Последние выплаты:</b>\n";
        $withdrawals = array_slice($user['withdrawals'], -3); // Последние 3 выплаты
        
        foreach ($withdrawals as $withdrawal) {
            $status = $withdrawal['status'] ?? 'pending';
            $amount = $withdrawal['coins_amount'] ?? 0;
            $currency = strtoupper($withdrawal['currency'] ?? '');
            $date = $withdrawal['created_at'] ?? date('Y-m-d', time());
            
            $text .= "→ {$amount} монет ({$currency}) - " . ucfirst($status) . " ({$date})\n";
        }
        $text .= "\n";
    }
    
    $text .= getTranslation('bot.open_app_withdraw', [], "🚀 Откройте приложение для вывода средств!", $userLanguage);

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getTranslation('bot.open_app', [], "🚀 Открыть приложение", $userLanguage),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getTranslation('bot.back', [], "🔙 Назад", $userLanguage),
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка приглашения друзей
 */
function handleInviteFriends($chatId, $userId) {
    $userData = loadUserData();

    if (!isset($userData[$userId])) {
        sendMessage($chatId, "❌ Пользователь не найден. Отправьте /start для регистрации.");
        return;
    }

    $user = $userData[$userId];
    $referrals = $user['referrals'] ?? [];
    $referralCount = count($referrals);
    $referralEarnings = $user['referral_earnings'] ?? 0;

    $referralLink = "https://t.me/" . BOT_USERNAME . "?start={$userId}";

    $text = "👥 <b>Реферальная программа</b>\n\n";
    $text .= "🎁 Получайте " . REFERRAL_BONUS_PERCENT . "% от заработка каждого приглашенного друга!\n\n";
    $text .= "📊 <b>Ваша статистика:</b>\n";
    $text .= "👥 Приглашено друзей: <b>{$referralCount}</b>\n";
    $text .= "💰 Заработано с рефералов: <b>{$referralEarnings} монет</b>\n\n";
    $text .= "🔗 <b>Ваша реферальная ссылка:</b>\n";
    $text .= "<code>{$referralLink}</code>\n\n";
    $text .= "📤 Поделитесь ссылкой с друзьями и зарабатывайте вместе!";

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => '📤 Поделиться ссылкой',
                    'switch_inline_query' => "🎉 Присоединяйся к UniQPaid и зарабатывай монеты за просмотр рекламы! {$referralLink}"
                ]
            ],
            [
                [
                    'text' => '🚀 Открыть приложение',
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => '🔙 Назад',
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка команды статистики
 */
function handleStatsCommand($chatId, $userId) {
    $userData = loadUserData();

    if (!isset($userData[$userId])) {
        sendMessage($chatId, "❌ Пользователь не найден. Отправьте /start для регистрации.");
        return;
    }

    $user = $userData[$userId];
    $balance = $user['balance'] ?? 0;
    $totalEarned = $user['total_earned'] ?? 0;
    $referralEarnings = $user['referral_earnings'] ?? 0;
    $withdrawalsCount = $user['withdrawals_count'] ?? 0;
    $referrals = $user['referrals'] ?? [];
    $registeredAt = $user['registered_at'] ?? time();

    $daysActive = max(1, floor((time() - $registeredAt) / 86400));
    $avgPerDay = round($totalEarned / $daysActive, 2);

    $text = "📊 <b>Ваша статистика</b>\n\n";
    $text .= "🪙 Текущий баланс: <b>{$balance} монет</b>\n";
    $text .= "📈 Всего заработано: <b>{$totalEarned} монет</b>\n";
    $text .= "👥 От рефералов: <b>{$referralEarnings} монет</b>\n";
    $text .= "💸 Выводов сделано: <b>{$withdrawalsCount}</b>\n\n";
    $text .= "📅 Дней в системе: <b>{$daysActive}</b>\n";
    $text .= "⚡ Среднее в день: <b>{$avgPerDay} монет</b>\n";
    $text .= "👥 Приглашено друзей: <b>" . count($referrals) . "</b>\n\n";
    $text .= "📅 Дата регистрации: <b>" . date('d.m.Y', $registeredAt) . "</b>";

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => '🚀 Открыть приложение',
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => '🔙 Назад',
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка команды помощи
 */
function handleHelpCommand($chatId) {
    $text = "❓ <b>Помощь по UniQPaid</b>\n\n";
    $text .= "🎯 <b>Как зарабатывать:</b>\n";
    $text .= "• Откройте мини-приложение\n";
    $text .= "• Смотрите рекламу и получайте " . COINS_PER_VIEW . " монет\n";
    $text .= "• Приглашайте друзей и получайте " . REFERRAL_BONUS_PERCENT . "% от их заработка\n\n";
    $text .= "💰 <b>Как выводить:</b>\n";
    $text .= "• Накопите монеты\n";
    $text .= "• Откройте приложение\n";
    $text .= "• Перейдите в раздел 'Заработок'\n";
    $text .= "• Запросите вывод на криптокошелек\n\n";
    // Правильно форматируем курс в помощи
    $formattedRate = COIN_VALUE_USD == floor(COIN_VALUE_USD) ? number_format(COIN_VALUE_USD, 0) : rtrim(rtrim(number_format(COIN_VALUE_USD, 3), '0'), '.');
    $text .= "💎 <b>Курс:</b> 1 монета = $" . $formattedRate . " USD\n\n";
    $text .= "📞 <b>Поддержка:</b> @support_uniqpaid";

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => '🚀 Открыть приложение',
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => '🔙 Назад',
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}
?>
