<?php
/**
 * Простой тест истории выплат без валидации
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/functions.php';

header('Content-Type: application/json');

// ID пользователя с выплатами
$testUserId = 5880288830;

try {
    // Загрузка данных пользователя
    $userData = loadUserData();
    if (!is_array($userData)) {
        throw new Exception('Не удалось загрузить данные пользователей');
    }

    // Получение истории выплат пользователя
    if (!isset($userData[$testUserId])) {
        throw new Exception('Пользователь не найден');
    }

    // Получаем историю выплат из данных пользователя
    $withdrawals = isset($userData[$testUserId]['withdrawals']) ? $userData[$testUserId]['withdrawals'] : [];

    // Сортируем выплаты по времени (от новых к старым)
    usort($withdrawals, function($a, $b) {
        return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
    });

    // Успешный ответ
    echo json_encode([
        'success' => true,
        'withdrawals' => $withdrawals,
        'total_count' => count($withdrawals),
        'user_id' => $testUserId,
        'debug_info' => [
            'user_exists' => isset($userData[$testUserId]),
            'withdrawals_field_exists' => isset($userData[$testUserId]['withdrawals']),
            'raw_withdrawals_count' => isset($userData[$testUserId]['withdrawals']) ? count($userData[$testUserId]['withdrawals']) : 0
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
