<?php
/**
 * api/security.php
 * Функции безопасности для защиты от мошенничества и накрутки монет
 */

// Константы для ограничений
define('MAX_AD_VIEWS_PER_HOUR', 20); // Максимальное количество просмотров рекламы в час
define('MAX_AD_VIEWS_PER_DAY', 100); // Максимальное количество просмотров рекламы в день
define('MAX_WITHDRAWALS_PER_DAY', 3); // Максимальное количество выводов в день
define('SUSPICIOUS_ACTIVITY_THRESHOLD', 5); // Порог подозрительной активности

/**
 * Проверяет, не превышен ли лимит просмотров рекламы
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkAdViewLimit($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита просмотров");
        return false;
    }
    
    // Инициализируем массив для отслеживания просмотров рекламы, если его еще нет
    if (!isset($userData[$userId]['ad_views_log'])) {
        $userData[$userId]['ad_views_log'] = [];
    }
    
    $currentTime = time();
    $hourAgo = $currentTime - 3600;
    $dayAgo = $currentTime - 86400;
    
    // Фильтруем просмотры за последний час
    $hourViews = array_filter($userData[$userId]['ad_views_log'], function($timestamp) use ($hourAgo) {
        return $timestamp >= $hourAgo;
    });
    
    // Фильтруем просмотры за последний день
    $dayViews = array_filter($userData[$userId]['ad_views_log'], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });
    
    // Очищаем старые записи (старше суток)
    $userData[$userId]['ad_views_log'] = $dayViews;
    
    // Проверяем лимиты
    if (count($hourViews) >= MAX_AD_VIEWS_PER_HOUR) {
        error_log("security WARNING: Пользователь {$userId} превысил часовой лимит просмотров рекламы");
        return false;
    }
    
    if (count($dayViews) >= MAX_AD_VIEWS_PER_DAY) {
        error_log("security WARNING: Пользователь {$userId} превысил дневной лимит просмотров рекламы");
        return false;
    }
    
    // Добавляем новый просмотр в лог
    $userData[$userId]['ad_views_log'][] = $currentTime;
    
    return true;
}

/**
 * Проверяет, не превышен ли лимит выводов средств
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkWithdrawalLimit($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита выводов");
        return false;
    }
    
    // Инициализируем массив для отслеживания выводов, если его еще нет
    if (!isset($userData[$userId]['withdrawal_log'])) {
        $userData[$userId]['withdrawal_log'] = [];
    }
    
    $currentTime = time();
    $dayAgo = $currentTime - 86400;
    
    // Фильтруем выводы за последний день
    $dayWithdrawals = array_filter($userData[$userId]['withdrawal_log'], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });
    
    // Очищаем старые записи (старше суток)
    $userData[$userId]['withdrawal_log'] = $dayWithdrawals;
    
    // Проверяем лимит
    if (count($dayWithdrawals) >= MAX_WITHDRAWALS_PER_DAY) {
        error_log("security WARNING: Пользователь {$userId} превысил дневной лимит выводов средств");
        return false;
    }
    
    // Добавляем новый вывод в лог
    $userData[$userId]['withdrawal_log'][] = $currentTime;
    
    return true;
}

/**
 * Проверяет баланс пользователя перед выводом средств
 * 
 * @param int $userId ID пользователя
 * @param int $amount Сумма для вывода
 * @param array $userData Данные всех пользователей
 * @return bool true, если баланс достаточен, false в противном случае
 */
function verifyBalance($userId, $amount, &$userData) {
    if (!isset($userData[$userId]) || !isset($userData[$userId]['balance'])) {
        error_log("security ERROR: Пользователь {$userId} не найден или нет баланса при проверке баланса");
        return false;
    }
    
    $balance = $userData[$userId]['balance'];
    
    // Проверяем, достаточно ли средств
    if ($balance < $amount) {
        error_log("security WARNING: Недостаточно средств у пользователя {$userId}. Запрошено: {$amount}, Доступно: {$balance}");
        return false;
    }
    
    // Проверяем, не превышает ли сумма вывода общее количество заработанных монет
    if (!isset($userData[$userId]['total_earned'])) {
        // Если поле total_earned не существует, создаем его на основе текущего баланса
        // и истории выводов
        $totalWithdrawn = 0;
        if (isset($userData[$userId]['withdrawals']) && is_array($userData[$userId]['withdrawals'])) {
            foreach ($userData[$userId]['withdrawals'] as $withdrawal) {
                if (isset($withdrawal['coins_amount'])) {
                    $totalWithdrawn += $withdrawal['coins_amount'];
                }
            }
        }
        $userData[$userId]['total_earned'] = $balance + $totalWithdrawn;
    }
    
    $totalEarned = $userData[$userId]['total_earned'];
    $totalWithdrawn = 0;
    
    if (isset($userData[$userId]['withdrawals']) && is_array($userData[$userId]['withdrawals'])) {
        foreach ($userData[$userId]['withdrawals'] as $withdrawal) {
            if (isset($withdrawal['coins_amount'])) {
                $totalWithdrawn += $withdrawal['coins_amount'];
            }
        }
    }
    
    $availableForWithdrawal = $totalEarned - $totalWithdrawn;
    
    if ($amount > $availableForWithdrawal) {
        error_log("security WARNING: Попытка вывести больше, чем было заработано. Пользователь: {$userId}, Запрошено: {$amount}, Доступно: {$availableForWithdrawal}");
        return false;
    }
    
    return true;
}

/**
 * Записывает операцию в журнал аудита
 * 
 * @param string $operation Тип операции
 * @param int $userId ID пользователя
 * @param array $data Дополнительные данные
 * @return bool true в случае успеха, false при ошибке
 */
function logAuditEvent($operation, $userId, $data = []) {
    $logFile = __DIR__ . '/audit.log';
    
    $logEntry = [
        'timestamp' => time(),
        'operation' => $operation,
        'user_id' => $userId,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'data' => $data
    ];
    
    $logLine = date('Y-m-d H:i:s') . ' | ' . json_encode($logEntry) . PHP_EOL;
    
    return file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX) !== false;
}

/**
 * Проверяет, не является ли активность пользователя подозрительной
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если активность не подозрительная, false в противном случае
 */
function checkSuspiciousActivity($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке подозрительной активности");
        return false;
    }
    
    // Инициализируем счетчик подозрительной активности, если его еще нет
    if (!isset($userData[$userId]['suspicious_activity'])) {
        $userData[$userId]['suspicious_activity'] = 0;
    }
    
    // Если счетчик превышает порог, блокируем пользователя
    if ($userData[$userId]['suspicious_activity'] >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
        if (!isset($userData[$userId]['blocked']) || !$userData[$userId]['blocked']) {
            $userData[$userId]['blocked'] = true;
            $userData[$userId]['blocked_at'] = time();
            error_log("security ALERT: Пользователь {$userId} заблокирован из-за подозрительной активности");
        }
        return false;
    }
    
    return true;
}

/**
 * Увеличивает счетчик подозрительной активности пользователя
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @param string $reason Причина увеличения счетчика
 * @return void
 */
function incrementSuspiciousActivity($userId, &$userData, $reason = '') {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при увеличении счетчика подозрительной активности");
        return;
    }
    
    // Инициализируем счетчик подозрительной активности, если его еще нет
    if (!isset($userData[$userId]['suspicious_activity'])) {
        $userData[$userId]['suspicious_activity'] = 0;
    }
    
    $userData[$userId]['suspicious_activity']++;
    
    error_log("security WARNING: Увеличен счетчик подозрительной активности для пользователя {$userId}. Новое значение: {$userData[$userId]['suspicious_activity']}. Причина: {$reason}");
    
    // Логируем событие
    logAuditEvent('suspicious_activity', $userId, ['reason' => $reason, 'count' => $userData[$userId]['suspicious_activity']]);
    
    // Если счетчик превысил порог, блокируем пользователя
    if ($userData[$userId]['suspicious_activity'] >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
        $userData[$userId]['blocked'] = true;
        $userData[$userId]['blocked_at'] = time();
        error_log("security ALERT: Пользователь {$userId} заблокирован из-за подозрительной активности");
        
        // Логируем блокировку
        logAuditEvent('user_blocked', $userId, ['reason' => 'suspicious_activity_threshold_reached']);
    }
}
?>
