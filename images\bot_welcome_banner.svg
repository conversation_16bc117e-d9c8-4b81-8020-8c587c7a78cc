<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Градиенты -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF8E7;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FFE4B5;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFF8E7;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="elegantGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="33%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="66%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>

    <radialGradient id="coinGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </radialGradient>
    
    <radialGradient id="centerGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#ff0080;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#8a2be2;stop-opacity:0.1" />
    </radialGradient>
    
    <!-- Фильтры для свечения -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Фон -->
  <rect width="600" height="400" fill="url(#bgGradient)"/>
  
  <!-- Центральное свечение -->
  <ellipse cx="300" cy="200" rx="250" ry="150" fill="url(#centerGlow)"/>
  
  <!-- Декоративные линии -->
  <line x1="0" y1="80" x2="600" y2="80" stroke="url(#neonGradient)" stroke-width="3" opacity="0.7" filter="url(#glow)"/>
  <line x1="0" y1="320" x2="600" y2="320" stroke="url(#neonGradient)" stroke-width="3" opacity="0.7" filter="url(#glow)"/>
  
  <!-- Угловые элементы -->
  <polygon points="0,0 50,0 0,50" fill="#00ffff" opacity="0.4"/>
  <polygon points="600,0 550,0 600,50" fill="#ff0080" opacity="0.4"/>
  <polygon points="0,400 50,400 0,350" fill="#8a2be2" opacity="0.4"/>
  <polygon points="600,400 550,400 600,350" fill="#39ff14" opacity="0.4"/>
  
  <!-- Главный заголовок -->
  <text x="300" y="60" text-anchor="middle" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="url(#neonGradient)" filter="url(#strongGlow)">UniQPaid</text>
  
  <!-- Подзаголовок -->
  <text x="300" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#00ffff" filter="url(#glow)">Crypto Rewards • Instant Payouts</text>
  
  <!-- Центральная композиция с монетами -->
  <!-- Большая центральная монета -->
  <circle cx="300" cy="200" r="70" fill="url(#coinGradient)" stroke="#ffdd00" stroke-width="4" filter="url(#strongGlow)"/>
  <circle cx="300" cy="200" r="55" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.8"/>
  <text x="300" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#ffffff" filter="url(#glow)">$</text>
  
  <!-- Орбитальные монеты -->
  <!-- Bitcoin -->
  <circle cx="180" cy="140" r="30" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="3" opacity="0.9"/>
  <text x="180" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">₿</text>
  
  <!-- Ethereum -->
  <circle cx="420" cy="140" r="30" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="3" opacity="0.9"/>
  <text x="420" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">Ξ</text>
  
  <!-- USDT -->
  <circle cx="180" cy="260" r="30" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="3" opacity="0.9"/>
  <text x="180" y="268" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">USDT</text>
  
  <!-- TRON -->
  <circle cx="420" cy="260" r="30" fill="url(#coinGradient)" stroke="#ffaa00" stroke-width="3" opacity="0.9"/>
  <text x="420" y="268" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">TRX</text>
  
  <!-- Соединительные линии -->
  <line x1="210" y1="155" x2="270" y2="185" stroke="#00ffff" stroke-width="2" opacity="0.5" filter="url(#glow)"/>
  <line x1="390" y1="155" x2="330" y2="185" stroke="#ff0080" stroke-width="2" opacity="0.5" filter="url(#glow)"/>
  <line x1="210" y1="245" x2="270" y2="215" stroke="#8a2be2" stroke-width="2" opacity="0.5" filter="url(#glow)"/>
  <line x1="390" y1="245" x2="330" y2="215" stroke="#39ff14" stroke-width="2" opacity="0.5" filter="url(#glow)"/>
  
  <!-- Информационные блоки -->
  <!-- Левый блок -->
  <rect x="50" y="300" width="120" height="60" rx="15" fill="none" stroke="#00ffff" stroke-width="2" opacity="0.6"/>
  <text x="110" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#00ffff">Watch Ads</text>
  <text x="110" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">Earn Coins</text>
  
  <!-- Правый блок -->
  <rect x="430" y="300" width="120" height="60" rx="15" fill="none" stroke="#ff0080" stroke-width="2" opacity="0.6"/>
  <text x="490" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ff0080">Instant</text>
  <text x="490" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">Payouts</text>
  
  <!-- Центральный блок снизу -->
  <rect x="240" y="300" width="120" height="60" rx="15" fill="none" stroke="#8a2be2" stroke-width="2" opacity="0.6"/>
  <text x="300" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#8a2be2">Referrals</text>
  <text x="300" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">10% Bonus</text>
  
  <!-- Декоративные элементы -->
  <circle cx="100" cy="200" r="5" fill="#00ffff" opacity="0.8" filter="url(#glow)"/>
  <circle cx="500" cy="200" r="5" fill="#ff0080" opacity="0.8" filter="url(#glow)"/>
  <circle cx="300" cy="120" r="3" fill="#8a2be2" opacity="0.9"/>
  <circle cx="300" cy="280" r="3" fill="#39ff14" opacity="0.9"/>
  
  <!-- Дополнительные неоновые акценты -->
  <path d="M 80 120 L 100 120 L 90 140 Z" fill="none" stroke="#00ffff" stroke-width="2" opacity="0.6"/>
  <path d="M 520 120 L 500 120 L 510 140 Z" fill="none" stroke="#ff0080" stroke-width="2" opacity="0.6"/>
  <path d="M 80 280 L 100 280 L 90 260 Z" fill="none" stroke="#8a2be2" stroke-width="2" opacity="0.6"/>
  <path d="M 520 280 L 500 280 L 510 260 Z" fill="none" stroke="#39ff14" stroke-width="2" opacity="0.6"/>
  
  <!-- Финальный акцент -->
  <text x="300" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="url(#neonGradient)" filter="url(#glow)">🚀 Start Earning Now! 💰</text>
</svg>
