# 🧮 Калькулятор NOWPayments в админке

## ✅ **Что создано:**

### 🎯 **Новый раздел в админке**
- **Файл**: `api/admin/calculator.php`
- **Название**: "Калькулятор NOWPayments"
- **Иконка**: 🧮 (calculator)
- **Доступ**: Только для авторизованных администраторов

### 🔧 **Функции калькулятора:**

#### 1️⃣ **Калькулятор конвертации валют**
- **Ввод суммы** (с поддержкой до 8 знаков после запятой)
- **Выбор исходной валюты** (USD, BTC, ETH, USDT TRC20, TON)
- **Выбор целевой валюты** (те же варианты)
- **Кнопка "Рассчитать"** для получения результата
- **Подробный результат** с estimated_amount и дополнительной информацией

#### 2️⃣ **Минимальные суммы для вывода**
- **Выбор валюты** из обязательных криптовалют (BTC, ETH, USDT TRC20, TON)
- **Кнопка "Получить минимальные суммы"**
- **Отображение результата** в JSON формате
- **Использует API endpoint** согласно документации NOWPayments

#### 3️⃣ **Список всех доступных валют**
- **Кнопка "Получить список всех валют"**
- **Отображение в виде badges** или JSON
- **Показывает все поддерживаемые** NOWPayments валюты

#### 4️⃣ **Документация и ссылки**
- **Прямые ссылки** на документацию NOWPayments API
- **Ссылка на минимальные суммы** для вывода
- **Ссылка на калькулятор цены**
- **Полная документация API**
- **Отображение настроек** API (замаскированные ключи)

### 🔗 **Интеграция с меню:**
- ✅ Добавлена ссылка в `api/admin/index.php`
- ✅ Добавлена ссылка в `api/admin/settings.php`
- ✅ Добавлена ссылка в `api/admin/cache_management.php`
- ✅ Добавлена обработка страницы в switch

### 📡 **API методы:**

#### **Существующие методы:**
- ✅ `getEstimateAmount()` - калькулятор конвертации
- ✅ `getMinWithdrawalAmount()` - минимальные суммы
- ✅ `getAvailableCurrencies()` - список валют

#### **Новые методы:**
- ✅ `getPriceCalculator()` - алиас для getEstimateAmount

### 🎨 **Дизайн и UX:**
- **Bootstrap карточки** для каждой функции
- **Иконки Bootstrap** для визуального разделения
- **Цветовая схема**: Primary, Info, Success
- **Адаптивная верстка** для мобильных устройств
- **Детальные результаты** с возможностью развернуть JSON

## 🔍 **Отладка Telegram Mini-App**

### 📋 **Создана инструкция:**
- **Файл**: `TELEGRAM_WEBAPP_DEBUG_GUIDE.md`
- **Содержание**: Полная инструкция по отладке в веб-версии Telegram

### 🎯 **Основные шаги отладки:**
1. **Открыть** web.telegram.org/k/
2. **Запустить** @uniqpaid_paid_bot
3. **Нажать** "Запустить приложение"
4. **F12** → найти iframe с приложением
5. **Переключить контекст** на iframe в консоли
6. **Отлаживать** как обычное веб-приложение

### 🔧 **Специфичные проверки:**
- **X-Frame-Options** заголовки
- **CORS** настройки
- **Telegram WebApp API** доступность
- **Сетевые запросы** к API
- **Локальное хранилище** данных

## 🚀 **Готово к использованию:**

### **Доступ к калькулятору:**
```
https://app.uniqpaid.com/api/admin/calculator.php
```

### **Функции доступны:**
- ✅ Конвертация валют в реальном времени (USD, BTC, ETH, USDT TRC20, TON)
- ✅ Проверка минимальных сумм для обязательных валют (BTC, ETH, USDT TRC20, TON)
- ✅ Получение списка всех поддерживаемых валют
- ✅ Прямые ссылки на документацию API
- ✅ Безопасный доступ только для админов

### **Отладка мини-приложения:**
- ✅ Полная инструкция по отладке в веб-Telegram
- ✅ Специфичные команды для проверки API
- ✅ Решения частых проблем
- ✅ Эмуляция мобильных устройств

## 🎉 **Преимущества:**

### **Для администратора:**
- 🔧 **Быстрая проверка** курсов валют
- 💰 **Контроль минимальных сумм** для выводов
- 📊 **Мониторинг доступных валют**
- 🔗 **Прямой доступ** к документации
- 🛡️ **Безопасный интерфейс** в админке

### **Для разработчика:**
- 🔍 **Полная инструкция** по отладке
- 🧪 **Тестирование в реальной среде** Telegram
- 🔧 **Проверка всех API** функций
- 📱 **Отладка на мобильных устройствах**
- 🚀 **Быстрое решение проблем**

---

**Калькулятор NOWPayments готов к использованию в админке!** 🎯💪
