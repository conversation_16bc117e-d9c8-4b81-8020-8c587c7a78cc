<?php
/**
 * Тестовая версия API для получения истории выплат без валидации
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/functions.php';

header('Content-Type: application/json');

try {
    // Получаем POST данные
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Для тестирования используем фиксированный ID пользователя
    $testUserId = 5880288830;
    
    error_log("getWithdrawalHistory_test INFO: Тестируем пользователя {$testUserId}");

    // Загрузка данных пользователя
    $userData = loadUserData();
    if (!is_array($userData)) {
        error_log("getWithdrawalHistory_test ERROR: loadUserData вернул не массив");
        throw new Exception('Ошибка загрузки данных пользователей');
    }

    // Получение истории выплат пользователя
    if (!isset($userData[$testUserId])) {
        error_log("getWithdrawalHistory_test ERROR: Пользователь {$testUserId} не найден");
        throw new Exception('Пользователь не найден');
    }

    // Получаем историю выплат из данных пользователя
    $withdrawals = isset($userData[$testUserId]['withdrawals']) ? $userData[$testUserId]['withdrawals'] : [];
    
    error_log("getWithdrawalHistory_test INFO: Найдено выплат: " . count($withdrawals));

    // Сортируем выплаты по времени (от новых к старым)
    usort($withdrawals, function($a, $b) {
        return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
    });

    // Успешный ответ
    echo json_encode([
        'withdrawals' => $withdrawals,
        'total_count' => count($withdrawals)
    ]);
    
    error_log("getWithdrawalHistory_test INFO: Успешно отправлена история выплат для пользователя {$testUserId} (всего: " . count($withdrawals) . ")");

} catch (Exception $e) {
    error_log("getWithdrawalHistory_test ERROR: " . $e->getMessage());
    echo json_encode([
        'error' => $e->getMessage()
    ]);
}
?>
