<?php
/**
 * api/admin/logout.php
 * Страница выхода из административной панели
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации
require_once __DIR__ . '/auth.php';

// Начинаем сессию
session_start();

// Выход из системы
logout();

// Перенаправляем на страницу входа
header('Location: login.php');
exit;
?>
