/* ======================================== */
/* PREMIUM EFFECTS - Утонченные эффекты премиум уровня */
/* ======================================== */

// Инициализация премиум эффектов
function initPremiumEffects() {
  console.log('✨ Инициализация премиум эффектов...');
  
  // Добавляем плавные анимации появления
  addFadeInAnimations();
  
  // Создаем утонченные интерактивные эффекты
  addPremiumInteractions();
  
  // Добавляем элегантные переходы
  addSmoothTransitions();
  
  // Инициализируем премиум курсор
  initPremiumCursor();
  
  console.log('🎩 Премиум эффекты активированы!');
}

// Плавные анимации появления элементов
function addFadeInAnimations() {
  const elements = document.querySelectorAll('.app-section, .action-button, .nav-button');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.style.animation = `premium-fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards`;
          entry.target.style.opacity = '1';
        }, index * 100);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  
  elements.forEach(element => {
    element.style.opacity = '0';
    observer.observe(element);
  });
}

// Премиум интерактивные эффекты
function addPremiumInteractions() {
  // Эффект для кнопок
  const buttons = document.querySelectorAll('.action-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      button.style.transform = 'translateY(-2px)';
      button.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.2)';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    });
    
    button.addEventListener('mousedown', () => {
      button.style.transform = 'translateY(0) scale(0.98)';
    });
    
    button.addEventListener('mouseup', () => {
      button.style.transform = 'translateY(-2px) scale(1)';
    });
  });
  
  // Эффект для карточек
  const cards = document.querySelectorAll('.app-section, .friends-block, .earn-block');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-1px)';
      card.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.2)';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0)';
      card.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    });
  });
}

// Плавные переходы между секциями
function addSmoothTransitions() {
  const navButtons = document.querySelectorAll('.nav-button');
  const sections = document.querySelectorAll('.app-section');
  
  navButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Добавляем эффект волны
      createPremiumRipple(button);
      
      // Плавный переход активного состояния
      navButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');
      
      // Анимация переключения секций
      sections.forEach(section => {
        if (!section.classList.contains('page-hidden')) {
          section.style.animation = 'premium-scale-out 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards';
          setTimeout(() => {
            section.classList.add('page-hidden');
          }, 300);
        }
      });
    });
  });
}

// Премиум курсор
function initPremiumCursor() {
  if (window.innerWidth <= 768) return; // Только для десктопа
  
  const cursor = document.createElement('div');
  cursor.className = 'premium-cursor';
  cursor.style.cssText = `
    position: fixed;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    mix-blend-mode: difference;
  `;
  document.body.appendChild(cursor);
  
  const cursorFollower = document.createElement('div');
  cursorFollower.className = 'premium-cursor-follower';
  cursorFollower.style.cssText = `
    position: fixed;
    width: 32px;
    height: 32px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  `;
  document.body.appendChild(cursorFollower);
  
  let mouseX = 0, mouseY = 0;
  let followerX = 0, followerY = 0;
  
  document.addEventListener('mousemove', (e) => {
    mouseX = e.clientX;
    mouseY = e.clientY;
    
    cursor.style.left = mouseX - 4 + 'px';
    cursor.style.top = mouseY - 4 + 'px';
  });
  
  // Плавное следование для большого курсора
  function updateFollower() {
    followerX += (mouseX - followerX) * 0.1;
    followerY += (mouseY - followerY) * 0.1;
    
    cursorFollower.style.left = followerX - 16 + 'px';
    cursorFollower.style.top = followerY - 16 + 'px';
    
    requestAnimationFrame(updateFollower);
  }
  updateFollower();
  
  // Эффекты при наведении
  const interactiveElements = document.querySelectorAll('button, a, input, [role="button"]');
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      cursor.style.transform = 'scale(1.5)';
      cursorFollower.style.transform = 'scale(1.5)';
      cursorFollower.style.borderColor = 'rgba(255, 255, 255, 0.6)';
    });
    
    element.addEventListener('mouseleave', () => {
      cursor.style.transform = 'scale(1)';
      cursorFollower.style.transform = 'scale(1)';
      cursorFollower.style.borderColor = 'rgba(255, 255, 255, 0.3)';
    });
  });
}

// Создание премиум волнового эффекта
function createPremiumRipple(element) {
  const ripple = document.createElement('div');
  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(0);
    animation: premium-ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    left: 50%;
    top: 50%;
    margin-left: -${size/2}px;
    margin-top: -${size/2}px;
    pointer-events: none;
  `;
  
  element.style.position = 'relative';
  element.appendChild(ripple);
  
  setTimeout(() => {
    ripple.remove();
  }, 600);
}

// Премиум уведомления
function showPremiumNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `premium-notification premium-notification-${type}`;
  notification.style.cssText = `
    position: fixed;
    top: 24px;
    right: 24px;
    background: rgba(26, 29, 32, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 16px 20px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 14px;
    z-index: 10000;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    transform: translateX(100%) scale(0.9);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 300px;
  `;
  
  if (type === 'success') {
    notification.style.borderColor = 'rgba(40, 167, 69, 0.3)';
    notification.style.background = 'rgba(40, 167, 69, 0.1)';
  } else if (type === 'error') {
    notification.style.borderColor = 'rgba(220, 53, 69, 0.3)';
    notification.style.background = 'rgba(220, 53, 69, 0.1)';
  }
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  // Анимация появления
  setTimeout(() => {
    notification.style.transform = 'translateX(0) scale(1)';
    notification.style.opacity = '1';
  }, 100);
  
  // Автоматическое скрытие
  setTimeout(() => {
    notification.style.transform = 'translateX(100%) scale(0.9)';
    notification.style.opacity = '0';
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 4000);
}

// Добавление CSS анимаций
function addPremiumCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes premium-ripple {
      to {
        transform: scale(2);
        opacity: 0;
      }
    }
    
    @keyframes premium-scale-out {
      to {
        transform: scale(0.95);
        opacity: 0;
      }
    }
    
    .premium-cursor {
      transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    .premium-cursor-follower {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    /* Улучшенные переходы для всех элементов */
    * {
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    /* Плавная прокрутка */
    html {
      scroll-behavior: smooth;
    }
    
    /* Улучшенный фокус */
    button:focus,
    input:focus,
    select:focus {
      outline: 2px solid rgba(255, 255, 255, 0.2);
      outline-offset: 2px;
    }
  `;
  document.head.appendChild(style);
}

// Эффект параллакса для фона
function addParallaxEffect() {
  let ticking = false;
  
  function updateParallax() {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.app-container::before, .app-container::after');
    
    parallaxElements.forEach(element => {
      const speed = 0.5;
      element.style.transform = `translateY(${scrolled * speed}px)`;
    });
    
    ticking = false;
  }
  
  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateParallax);
      ticking = true;
    }
  }
  
  window.addEventListener('scroll', requestTick);
}

// Инициализация всех премиум эффектов
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    addPremiumCSS();
    initPremiumEffects();
    addParallaxEffect();
    
    // Показываем приветственное уведомление
    setTimeout(() => {
      showPremiumNotification('🎩 Премиум дизайн активирован', 'success');
    }, 1000);
  }, 300);
});

// Экспорт функций
window.PremiumEffects = {
  showPremiumNotification,
  createPremiumRipple,
  initPremiumCursor,
  addFadeInAnimations
};
