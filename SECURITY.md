# Настройки безопасности UniQPaid

## Обзор защиты

Проект защищен с помощью `.htaccess` файлов в критически важных директориях:

### 1. Корневая директория (`.htaccess`)
- Защита системных файлов (.git, .log, .bak, .zip)
- Запрет просмотра директорий
- Настройки безопасности заголовков (XSS, CSRF защита)
- Кэширование и сжатие статических файлов
- Разрешен доступ к публичным файлам (index.html, main.js, CSS, изображения)

### 2. API директория (`api/.htaccess`)
- **РАЗРЕШЕН** доступ к публичным API endpoints:
  - getUserData.php
  - recordAdView.php
  - requestWithdrawal.php
  - getWithdrawalHistory.php
  - getReferralStats.php
  - registerReferral.php
  - getAppSettings.php
  - getUserLanguage.php
  - getCurrencyData.php
  - getAvailableCurrencies.php
  - calculateWithdrawalAmount.php
  - cancelWithdrawal.php
  - checkUserWithdrawals.php
  - check_payout_status.php
  - check_rejected_payouts.php
  - validate_initdata.php
  - withdrawal_callback.php

- **ЗАПРЕЩЕН** доступ к служебным файлам:
  - config.php (конфигурация)
  - db_mock.php (база данных)
  - security.php (функции безопасности)
  - NOWPaymentsAPI.php (API классы)
  - nowpayments_api.php
  - user_data.json (данные пользователей)
  - *.log (логи)
  - *.bak, *.backup (резервные копии)

### 3. Админ панель (`api/admin/.htaccess`)
- **ПОЛНЫЙ ЗАПРЕТ** доступа извне
- Разрешен доступ только с localhost (127.0.0.1)
- Дополнительная защита всех файлов

### 4. Bot директория (`bot/.htaccess`)
- **РАЗРЕШЕН** доступ только к:
  - webhook.php (для Telegram webhook)
  - set_webhook.php (для настройки)
- **ЗАПРЕЩЕН** доступ к:
  - config.php (конфигурация бота)
  - *.md (документация)
  - *.log (логи)

### 5. Includes директория (`includes/.htaccess`)
- **ПОЛНЫЙ ЗАПРЕТ** доступа к PHP классам
- Запрет просмотра директории

### 6. Instruction директория (`instruction/.htaccess`)
- **ПОЛНЫЙ ЗАПРЕТ** доступа к документации
- Защита конфиденциальной информации

### 7. Locales директория (`locales/.htaccess`)
- **РАЗРЕШЕН** доступ к JSON файлам локализации
- Правильный Content-Type для JSON
- Запрет просмотра директории

## Что защищено

✅ **Конфигурационные файлы** - config.php, настройки API
✅ **Данные пользователей** - user_data.json
✅ **Логи и отладочная информация** - *.log файлы
✅ **Служебные классы** - PHP классы в includes/
✅ **Документация** - инструкции и техническая документация
✅ **Админ панель** - полная защита от внешнего доступа
✅ **Системные файлы** - .git, .htaccess, backup файлы

## Что остается доступным

✅ **Публичные API** - для работы приложения
✅ **Статические ресурсы** - CSS, JS, изображения
✅ **Локализация** - JSON файлы переводов
✅ **Telegram webhook** - для работы бота
✅ **Основное приложение** - index.html и main.js

## Дополнительные меры безопасности

1. **Заголовки безопасности**:
   - X-Content-Type-Options: nosniff
   - X-Frame-Options: DENY
   - X-XSS-Protection: 1; mode=block
   - Content-Security-Policy

2. **Производительность**:
   - Кэширование статических файлов
   - Сжатие контента (gzip)

3. **Защита от атак**:
   - Запрет просмотра директорий
   - Защита от MIME-type sniffing
   - Защита от XSS атак

## Проверка безопасности

После установки .htaccess файлов проверьте:

1. ❌ Недоступны: `/api/config.php`, `/api/user_data.json`
2. ❌ Недоступны: `/bot/config.php`, `/includes/`
3. ❌ Недоступны: `/instruction/`, `/api/admin/`
4. ✅ Доступны: `/api/getUserData.php`, `/images/`, `/locales/ru.json`
5. ✅ Работает: основное приложение и все функции

## Важно

- Все .htaccess файлы настроены так, чтобы НЕ нарушить работу приложения
- Публичные API endpoints остаются доступными
- Telegram webhook продолжает работать
- Статические ресурсы загружаются нормально
