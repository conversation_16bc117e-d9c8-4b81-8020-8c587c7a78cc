# 🔄 МИГРАЦИЯ С test2 НА test3

## 🎯 Цель
Переместить приложение с `https://app.uniqpaid.com/test2/` на `https://app.uniqpaid.com/test3/` для полного сброса кэша Telegram и решения проблемы с `main.js:4294`.

## ✅ Изменения в коде (ВЫПОЛНЕНО)

Все файлы уже обновлены с `test2` → `test3`:

### 📁 Основные конфигурационные файлы:
- ✅ `bot/config.php` - webhook и webapp URL
- ✅ `api/config.php` - без изменений (локальные пути)

### 📁 Документация и инструкции:
- ✅ `bot/README.md`
- ✅ `bot/WEBHOOK_SETUP.md`
- ✅ `bot/TROUBLESHOOTING.md`
- ✅ `SECURITY_CHECK.md`
- ✅ `FINAL_DEPLOYMENT_INSTRUCTIONS.md`

### 📁 Скрипты для работы с изображениями:
- ✅ `bot/reload_image.php`
- ✅ `bot/update_super_banner.php`
- ✅ `bot/SUPER_BANNER_UPDATE.md`
- ✅ `bot/SVG_TO_PNG_CONVERSION.md`

## 🚀 Инструкции по развертыванию

### Шаг 1: Создание папки test3 на сервере
```bash
# На сервере создайте новую папку
mkdir /path/to/app.uniqpaid.com/test3
```

### Шаг 2: Копирование всех файлов
```bash
# Скопируйте все файлы из test2 в test3
cp -r /path/to/app.uniqpaid.com/test2/* /path/to/app.uniqpaid.com/test3/
```

### Шаг 3: Загрузка обновленных файлов
Загрузите на сервер в папку `test3/` следующие обновленные файлы:

#### 🔧 Основные файлы:
- `bot/config.php` (обновленные URL)
- `index.html` (с версионностью main.js)
- `main.js` (исправленная версия)

#### 📚 Документация (опционально):
- `bot/README.md`
- `bot/WEBHOOK_SETUP.md`
- `bot/TROUBLESHOOTING.md`
- `SECURITY_CHECK.md`
- `FINAL_DEPLOYMENT_INSTRUCTIONS.md`
- `bot/SUPER_BANNER_UPDATE.md`
- `bot/SVG_TO_PNG_CONVERSION.md`

#### 🖼️ Скрипты изображений:
- `bot/reload_image.php`
- `bot/update_super_banner.php`

### Шаг 4: Установка webhook для нового URL
Откройте в браузере:
```
https://app.uniqpaid.com/test3/bot/set_webhook.php
```

### Шаг 5: Проверка работы
1. **Проверьте webhook:**
   ```
   https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/getWebhookInfo
   ```
   Должно показать: `"url": "https://app.uniqpaid.com/test3/bot/webhook.php"`

2. **Протестируйте бота:**
   - Отправьте `/start` боту в Telegram
   - Нажмите "🚀 Запустить приложение"
   - Проверьте, что открывается `test3` версия

3. **Проверьте приложение:**
   ```
   https://app.uniqpaid.com/test3/
   ```

## 🎉 Результат

### ✅ Что будет исправлено:
1. **Полный сброс кэша Telegram** - новый URL заставит Telegram загрузить все файлы заново
2. **Исчезновение ошибки main.js:4294** - будет загружена новая версия с версионностью
3. **Корректная работа системы выплат** - все функции будут работать правильно

### 🔗 Новые URL:
- **Приложение:** `https://app.uniqpaid.com/test3/`
- **Админ-панель:** `https://app.uniqpaid.com/test3/api/admin/`
- **Webhook:** `https://app.uniqpaid.com/test3/bot/webhook.php`

## 🛠️ Дополнительные действия

### Если нужно удалить старый webhook:
```bash
curl -X POST "https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/deleteWebhook"
```

### Проверка безопасности для test3:
Обновите файл `SECURITY_CHECK.md` и проверьте все URL с `test3`.

### Обновление .htaccess файлов:
Убедитесь, что все `.htaccess` файлы скопированы в папку `test3/`.

## 📊 Контрольный список

- [ ] Создана папка `test3/` на сервере
- [ ] Скопированы все файлы из `test2/` в `test3/`
- [ ] Загружены обновленные файлы с новыми URL
- [ ] Установлен webhook для `test3/bot/webhook.php`
- [ ] Протестирован бот (команда `/start`)
- [ ] Проверено открытие приложения из бота
- [ ] Проверена работа системы выплат
- [ ] Убедились, что ошибка `main.js:4294` исчезла

## 🎯 Финальная проверка

После выполнения всех шагов:

1. **Откройте приложение:** `https://app.uniqpaid.com/test3/`
2. **Проверьте консоль браузера** - не должно быть ошибок
3. **Протестируйте выплаты** - все должно работать корректно
4. **Проверьте админ-панель:** `https://app.uniqpaid.com/test3/api/admin/`

**🎉 Готово! Приложение успешно мигрировано на test3 с полным сбросом кэша!**

---

**Дата:** 06.01.2025  
**Статус:** ✅ ГОТОВО К РАЗВЕРТЫВАНИЮ
