<?php
/**
 * api/admin/auth.php
 * Функции аутентификации и авторизации для административной панели
 */

// Файл с учетными данными администратора
define('ADMIN_CREDENTIALS_FILE', __DIR__ . '/admin_credentials.json');

/**
 * Проверяет, аутентифицирован ли пользователь
 * 
 * @return bool true, если пользователь аутентифицирован, false в противном случае
 */
function isAuthenticated() {
    return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
}

/**
 * Аутентифицирует пользователя
 * 
 * @param string $username Имя пользователя
 * @param string $password Пароль
 * @return bool true, если аутентификация успешна, false в противном случае
 */
function authenticate($username, $password) {
    $credentials = getAdminCredentials();
    
    if ($credentials === false) {
        // Если файл с учетными данными не существует, создаем его с учетными данными по умолчанию
        $credentials = [
            'username' => 'admin',
            'password' => password_hash('admin', PASSWORD_DEFAULT)
        ];
        saveAdminCredentials($credentials);
    }
    
    // Проверяем учетные данные
    if ($username === $credentials['username'] && password_verify($password, $credentials['password'])) {
        $_SESSION['admin_authenticated'] = true;
        $_SESSION['admin_username'] = $username;
        
        // Логируем успешный вход
        error_log("AUTH INFO: Успешный вход администратора {$username} с IP " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        
        return true;
    }
    
    // Логируем неудачную попытку входа
    error_log("AUTH WARNING: Неудачная попытка входа с логином {$username} с IP " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    
    return false;
}

/**
 * Выход из системы
 */
function logout() {
    // Логируем выход
    if (isset($_SESSION['admin_username'])) {
        error_log("AUTH INFO: Выход администратора {$_SESSION['admin_username']} с IP " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    }
    
    // Уничтожаем сессию
    $_SESSION = [];
    
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    session_destroy();
}

/**
 * Получает учетные данные администратора из файла
 * 
 * @return array|false Учетные данные администратора или false в случае ошибки
 */
function getAdminCredentials() {
    if (!file_exists(ADMIN_CREDENTIALS_FILE)) {
        return false;
    }
    
    $jsonData = file_get_contents(ADMIN_CREDENTIALS_FILE);
    if ($jsonData === false) {
        error_log("AUTH ERROR: Не удалось прочитать файл с учетными данными администратора");
        return false;
    }
    
    $credentials = json_decode($jsonData, true);
    if ($credentials === null && json_last_error() !== JSON_ERROR_NONE) {
        error_log("AUTH ERROR: Ошибка декодирования JSON: " . json_last_error_msg());
        return false;
    }
    
    return $credentials;
}

/**
 * Сохраняет учетные данные администратора в файл
 * 
 * @param array $credentials Учетные данные администратора
 * @return bool true в случае успеха, false при ошибке
 */
function saveAdminCredentials($credentials) {
    $jsonData = json_encode($credentials, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($jsonData === false) {
        error_log("AUTH ERROR: Ошибка кодирования JSON: " . json_last_error_msg());
        return false;
    }
    
    $result = file_put_contents(ADMIN_CREDENTIALS_FILE, $jsonData, LOCK_EX);
    if ($result === false) {
        error_log("AUTH ERROR: Не удалось сохранить файл с учетными данными администратора");
        return false;
    }
    
    // Устанавливаем права доступа к файлу
    chmod(ADMIN_CREDENTIALS_FILE, 0600);
    
    return true;
}

/**
 * Изменяет учетные данные администратора
 * 
 * @param string $newUsername Новое имя пользователя
 * @param string $newPassword Новый пароль
 * @return bool true в случае успеха, false при ошибке
 */
function changeAdminCredentials($newUsername, $newPassword) {
    $credentials = [
        'username' => $newUsername,
        'password' => password_hash($newPassword, PASSWORD_DEFAULT)
    ];
    
    $result = saveAdminCredentials($credentials);
    
    if ($result) {
        // Логируем изменение учетных данных
        error_log("AUTH INFO: Учетные данные администратора изменены пользователем {$_SESSION['admin_username']} с IP " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    }
    
    return $result;
}
?>
