<?php
/**
 * Простой тест API NOWPayments
 */

require_once 'api/config.php';
require_once 'api/NOWPaymentsAPI.php';

echo "<h1>🔍 Простой тест API NOWPayments</h1>";

// Тестируем с реальным ID выплаты
$payoutId = '5003161946';

echo "<h2>Тестируем выплату: {$payoutId}</h2>";

try {
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    echo "<p>🔄 Отправляем запрос...</p>";
    
    $response = $api->getPayoutStatus($payoutId);
    
    if ($response) {
        echo "<h3>✅ Ответ получен!</h3>";
        echo "<pre style='background: #f0f0f0; padding: 10px;'>";
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        echo "</pre>";
        
        if (isset($response['status'])) {
            echo "<p style='color: green;'><strong>Статус найден:</strong> {$response['status']}</p>";
        } else {
            echo "<p style='color: red;'><strong>Поле 'status' не найдено!</strong></p>";
            echo "<p>Доступные поля: " . implode(', ', array_keys($response)) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Не удалось получить ответ от API</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Также тестируем завершенную выплату
echo "<hr>";
echo "<h2>Тестируем завершенную выплату: 5003151124</h2>";

try {
    $response2 = $api->getPayoutStatus('5003151124');
    
    if ($response2) {
        echo "<h3>✅ Ответ получен!</h3>";
        echo "<pre style='background: #f0f0f0; padding: 10px;'>";
        echo json_encode($response2, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        echo "</pre>";
        
        if (isset($response2['status'])) {
            echo "<p style='color: green;'><strong>Статус найден:</strong> {$response2['status']}</p>";
        } else {
            echo "<p style='color: red;'><strong>Поле 'status' не найдено!</strong></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Не удалось получить ответ от API</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Проверяем логи
echo "<hr>";
echo "<h2>📋 Последние записи в логе:</h2>";

$logFile = 'api/error.log';
if (file_exists($logFile)) {
    $logs = file($logFile);
    $recentLogs = array_slice($logs, -20); // Последние 20 строк
    
    echo "<pre style='background: #f9f9f9; padding: 10px; max-height: 300px; overflow-y: auto;'>";
    foreach ($recentLogs as $log) {
        if (strpos($log, 'NOWPaymentsAPI') !== false) {
            echo htmlspecialchars($log);
        }
    }
    echo "</pre>";
} else {
    echo "<p>Лог файл не найден</p>";
}
?>
