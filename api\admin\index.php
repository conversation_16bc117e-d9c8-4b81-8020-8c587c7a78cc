<?php
/**
 * api/admin/index.php
 * Главная страница административной панели
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in admin/index.php'); 
    die('Ошибка: Не удалось загрузить config.php'); 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in admin/index.php'); 
    die('Ошибка: Не удалось загрузить db_mock.php'); 
}
if (!(@require_once __DIR__ . '/../security.php')) { 
    http_response_code(500); 
    error_log('FATAL: security.php not found in admin/index.php'); 
    die('Ошибка: Не удалось загрузить security.php'); 
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Получение статистики
$totalUsers = count($userData);
$totalBalance = 0;
$totalReferrals = 0;
$totalWithdrawals = 0;
$totalWithdrawalAmount = 0;
$totalAdViews = 0;
$blockedUsers = 0;
$suspiciousUsers = 0;

foreach ($userData as $userId => $user) {
    $totalBalance += $user['balance'] ?? 0;
    $totalReferrals += $user['referrals_count'] ?? 0;
    
    if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
        $totalWithdrawals += count($user['withdrawals']);
        foreach ($user['withdrawals'] as $withdrawal) {
            $totalWithdrawalAmount += $withdrawal['coins_amount'] ?? 0;
        }
    }
    
    if (isset($user['ad_views_log']) && is_array($user['ad_views_log'])) {
        $totalAdViews += count($user['ad_views_log']);
    }
    
    if (isset($user['blocked']) && $user['blocked']) {
        $blockedUsers++;
    }
    
    if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0) {
        $suspiciousUsers++;
    }
}

// Получение последних действий из журнала аудита
$auditLog = [];
$auditLogFile = __DIR__ . '/../audit.log';
if (file_exists($auditLogFile)) {
    $auditLog = array_slice(array_reverse(file($auditLogFile)), 0, 10);
}

// Обработка действий
$actionMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'clear_audit_log':
            if (file_exists($auditLogFile)) {
                file_put_contents($auditLogFile, '');
                $actionMessage = 'Журнал аудита успешно очищен';
                $auditLog = [];
            }
            break;
    }
}

// Получение активной страницы
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// Заголовок страницы
$pageTitle = 'Панель управления';
switch ($page) {
    case 'users':
        $pageTitle = 'Управление пользователями';
        break;
    case 'stats':
        $pageTitle = 'Статистика';
        break;
    case 'settings':
        $pageTitle = 'Настройки';
        break;
    case 'security':
        $pageTitle = 'Безопасность';
        break;
    case 'balance':
        $pageTitle = 'Баланс NOWPayments';
        break;
    case 'calculator':
        $pageTitle = 'Калькулятор NOWPayments';
        break;
    case 'cache':
        $pageTitle = 'Управление кэшем';
        break;
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><?php echo $pageTitle; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">Обновить</a>
                        <a href="cache_management.php" class="btn btn-sm btn-primary">
                            <i class="bi bi-arrow-clockwise"></i> Управление кэшем
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($actionMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $actionMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Карточки с основной статистикой -->
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Пользователей</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalUsers; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-people fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Общий баланс</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalBalance; ?> монет</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-currency-exchange fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Просмотров рекламы</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalAdViews; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-eye fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Выводов средств</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalWithdrawals; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-cash-stack fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Дашборд NOWPayments -->
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card border-left-info shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-info">💰 Баланс и курсы NOWPayments</h6>
                            <div>
                                <button onclick="refreshDashboard()" class="btn btn-sm btn-outline-info me-2">
                                    <i class="bi bi-arrow-clockwise"></i> Обновить
                                </button>
                                <a href="balance.php" class="btn btn-sm btn-info">Подробнее</a>
                            </div>
                        </div>
                        <div class="card-body" id="nowpayments-dashboard">
                            <?php
                            // Получаем актуальные данные (точно так же как в мониторинге)
                            try {
                                require_once __DIR__ . '/../NOWPaymentsAPI.php';
                                $nowApi = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

                                // Получаем баланс точно так же как в мониторинге
                                $nowBalance = $nowApi->getAccountBalance();

                                // Доступные валюты для отображения
                                $availableCurrencies = ['eth', 'btc', 'usdttrc20', 'trx'];

                                if ($nowBalance) {
                                    // Рассчитываем общий баланс точно так же как в мониторинге
                                    $totalBalanceUSD = 0;

                                    foreach ($nowBalance as $currency => $data) {
                                        $amount = is_array($data) ? ($data['amount'] ?? 0) : $data;
                                        if ($amount > 0) {
                                            try {
                                                $estimate = $nowApi->getEstimateAmount($amount, $currency, 'usd');
                                                if (isset($estimate['estimated_amount'])) {
                                                    $totalBalanceUSD += $estimate['estimated_amount'];
                                                }
                                            } catch (Exception $e) {
                                                // Игнорируем ошибки конвертации
                                            }
                                        }
                                    }

                                    echo '<div class="row">';

                                    // Отображаем все 4 доступные валюты
                                    foreach ($availableCurrencies as $currency) {
                                        // Получаем баланс для валюты из реального API ответа
                                        $amount = 0;
                                        if (isset($nowBalance[$currency])) {
                                            $balanceData = $nowBalance[$currency];
                                            $amount = is_array($balanceData) ? ($balanceData['amount'] ?? 0) : $balanceData;
                                        }

                                        // Рассчитываем USD эквивалент точно так же как в мониторинге
                                        $usdValue = 0;
                                        if ($amount > 0) {
                                            try {
                                                $estimate = $nowApi->getEstimateAmount($amount, $currency, 'usd');
                                                if (isset($estimate['estimated_amount'])) {
                                                    $usdValue = $estimate['estimated_amount'];
                                                }
                                            } catch (Exception $e) {
                                                // Игнорируем ошибки конвертации
                                            }
                                        }

                                        // Получаем минимум для валюты
                                        $minAmount = 0;
                                        try {
                                            $min = $nowApi->getMinWithdrawalAmount($currency);
                                            $minAmount = $min ?: 0;
                                        } catch (Exception $e) {
                                            // Игнорируем ошибки получения минимума
                                        }

                                        // Определяем статус
                                        $isEmpty = $amount <= 0.00001;
                                        $isLow = $minAmount > 0 && $amount > 0 && $amount < ($minAmount * 10);

                                        if ($isEmpty) {
                                            $statusText = 'НЕТ БАЛАНСА';
                                            $statusClass = 'border-danger';
                                            $textClass = 'text-danger';
                                            $badgeClass = 'bg-danger';
                                        } elseif ($isLow) {
                                            $statusText = 'НИЗКИЙ';
                                            $statusClass = 'border-warning';
                                            $textClass = 'text-warning';
                                            $badgeClass = 'bg-warning text-dark';
                                        } else {
                                            $statusText = 'ДОСТАТОЧНО';
                                            $statusClass = 'border-success';
                                            $textClass = 'text-success';
                                            $badgeClass = 'bg-success';
                                        }

                                        echo '<div class="col-md-3 mb-3">';
                                        echo '<div class="card ' . $statusClass . ' h-100">';
                                        echo '<div class="card-body p-3">';
                                        echo '<div class="d-flex justify-content-between align-items-center">';
                                        echo '<div>';
                                        echo '<div class="text-xs font-weight-bold text-uppercase ' . $textClass . '">';
                                        echo strtoupper($currency);
                                        echo ' <span class="badge ' . $badgeClass . '">' . $statusText . '</span>';
                                        echo '</div>';
                                        echo '<div class="h6 mb-1 font-weight-bold">' . number_format($amount, 8) . '</div>';
                                        echo '<div class="text-xs text-muted">≈ $' . number_format($usdValue, 2) . '</div>';
                                        if ($minAmount > 0) {
                                            echo '<div class="text-xs text-info">Мин: ' . number_format($minAmount, 8) . '</div>';
                                        }
                                        echo '</div>';
                                        echo '<div class="text-end">';
                                        $icons = ['btc' => '₿', 'eth' => 'Ξ', 'usdttrc20' => '💲', 'trx' => '🔺'];
                                        echo '<span class="h4">' . ($icons[$currency] ?? '💰') . '</span>';
                                        echo '</div>';
                                        echo '</div>';
                                        echo '</div>';
                                        echo '</div>';
                                        echo '</div>';
                                    }

                                    echo '</div>';

                                    // Информационная панель
                                    echo '<div class="row mt-3">';
                                    echo '<div class="col-md-6 mb-3">';
                                    echo '<div class="card border-primary h-100">';
                                    echo '<div class="card-body p-3 text-center">';
                                    echo '<div class="text-xs font-weight-bold text-primary text-uppercase mb-2">Общая стоимость</div>';
                                    echo '<div class="h4 mb-1 font-weight-bold text-primary">$' . number_format($totalBalanceUSD, 2) . '</div>';
                                    echo '<div class="text-xs text-muted">Обновлено: ' . date('H:i:s') . '</div>';
                                    echo '</div>';
                                    echo '</div>';
                                    echo '</div>';

                                    echo '<div class="col-md-6 mb-3">';
                                    echo '<div class="card border-info h-100">';
                                    echo '<div class="card-body p-3 text-center">';
                                    echo '<div class="text-xs font-weight-bold text-info text-uppercase mb-2">Статус валют</div>';
                                    $activeCount = count(array_filter($availableCurrencies, function($curr) use ($nowBalance) {
                                        if (!isset($nowBalance[$curr])) return false;
                                        $data = $nowBalance[$curr];
                                        $amount = is_array($data) ? ($data['amount'] ?? 0) : $data;
                                        return $amount > 0.00001;
                                    }));
                                    echo '<div class="h4 mb-1 font-weight-bold text-info">' . $activeCount . ' / 4</div>';
                                    echo '<div class="text-xs text-muted">Валют с балансом</div>';
                                    echo '</div>';
                                    echo '</div>';
                                    echo '</div>';
                                    echo '</div>';

                                    echo '</div>';

                                    // Статус системы на основе реального баланса
                                    if ($totalBalanceUSD > 10) {
                                        echo '<div class="alert alert-success mt-3" role="alert">';
                                        echo '<i class="bi bi-check-circle"></i> ';
                                        echo '<strong>Система готова к работе</strong> - достаточно средств для выплат ($' . number_format($totalBalanceUSD, 2) . ')';
                                        echo '</div>';
                                    } elseif ($totalBalanceUSD > 0) {
                                        echo '<div class="alert alert-warning mt-3" role="alert">';
                                        echo '<i class="bi bi-exclamation-triangle"></i> ';
                                        echo '<strong>Низкий баланс</strong> - рекомендуется пополнение ($' . number_format($totalBalanceUSD, 2) . ')';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="alert alert-info mt-3" role="alert">';
                                        echo '<i class="bi bi-info-circle"></i> ';
                                        echo '<strong>Требуется пополнение</strong> - нет средств для выплат';
                                        echo '</div>';
                                    }

                                } else {
                                    echo '<div class="alert alert-danger" role="alert">';
                                    echo '<i class="bi bi-exclamation-triangle"></i> ';
                                    echo '<strong>Ошибка подключения к NOWPayments API</strong>';
                                    echo '<br>Проверьте API ключи в настройках';
                                    echo '</div>';
                                }
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger" role="alert">';
                                echo '<i class="bi bi-bug"></i> ';
                                echo '<strong>Критическая ошибка:</strong> ' . htmlspecialchars($e->getMessage());
                                echo '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Дополнительная статистика -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Последние действия</h6>
                            <form method="post">
                                <input type="hidden" name="action" value="clear_audit_log">
                                <button type="submit" class="btn btn-sm btn-outline-danger">Очистить журнал</button>
                            </form>
                        </div>
                        <div class="card-body">
                            <?php if (empty($auditLog)): ?>
                                <p class="text-center">Журнал аудита пуст</p>
                            <?php else: ?>
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <?php foreach ($auditLog as $logEntry): ?>
                                        <div class="log-entry small"><?php echo htmlspecialchars($logEntry); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Безопасность</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-danger text-white shadow">
                                        <div class="card-body">
                                            Заблокировано пользователей
                                            <div class="text-white-50 small"><?php echo $blockedUsers; ?></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-warning text-white shadow">
                                        <div class="card-body">
                                            Подозрительная активность
                                            <div class="text-white-50 small"><?php echo $suspiciousUsers; ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <a href="security.php" class="btn btn-primary">Подробнее</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Функция обновления дашборда
function refreshDashboard() {
    const dashboardElement = document.getElementById('nowpayments-dashboard');
    if (!dashboardElement) return;

    // Показываем индикатор загрузки
    dashboardElement.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">Загрузка...</span>
            </div>
            <div class="mt-2">Обновление данных...</div>
        </div>
    `;

    // Обновляем страницу для получения свежих данных
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Автоматическое обновление каждые 5 минут
setInterval(() => {
    console.log('Автоматическое обновление дашборда...');
    refreshDashboard();
}, 300000); // 5 минут

// Обновление времени каждую секунду
setInterval(() => {
    const timeElements = document.querySelectorAll('.update-time');
    timeElements.forEach(element => {
        element.textContent = 'Обновлено: ' + new Date().toLocaleTimeString();
    });
}, 1000);
</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
