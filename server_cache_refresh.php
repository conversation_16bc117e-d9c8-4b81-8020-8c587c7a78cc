<?php
/**
 * Скрипт для обновления кэша Telegram на сервере
 * Загрузить на app.uniqpaid.com и запустить через браузер
 */

header('Content-Type: text/html; charset=utf-8');

// Правильный токен бота @uniqpaid_paid_bot
$botToken = '8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA';
$botUsername = 'uniqpaid_paid_bot';
$apiUrl = "https://api.telegram.org/bot{$botToken}/";
$webhookUrl = 'https://app.uniqpaid.com/test3/bot/webhook.php';
$webappUrl = 'https://app.uniqpaid.com/test3/';

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Обновление кэша Telegram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #00ffff;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            font-weight: bold;
        }
        .success { background: rgba(0, 255, 0, 0.2); border: 2px solid #00ff00; color: #00ff88; }
        .error { background: rgba(255, 0, 0, 0.2); border: 2px solid #ff0000; color: #ff6666; }
        .info { background: rgba(0, 255, 255, 0.2); border: 2px solid #00ffff; color: #00ffff; }
        .warning { background: rgba(255, 255, 0, 0.2); border: 2px solid #ffff00; color: #ffff88; }
        .code {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        button {
            background: linear-gradient(135deg, #00ffff, #0080ff);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: linear-gradient(135deg, #00ff88, #00ffff);
            transform: scale(1.05);
        }
        .step {
            border-left: 4px solid #00ffff;
            padding-left: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Обновление кэша Telegram мини-приложения</h1>
        
        <div class="status info">
            📋 Информация о боте:
            <div class="code">Бот: @uniqpaid_paid_bot
Токен: <?php echo substr($botToken, 0, 10); ?>...
Webhook: <?php echo $webhookUrl; ?>
WebApp: https://app.uniqpaid.com/test3/</div>
        </div>

        <?php
        /**
         * Функция для запроса к Telegram API
         */
        function telegramRequest($method, $data = []) {
            global $apiUrl;
            
            $url = $apiUrl . $method;
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($result === false || $error) {
                return ['error' => "cURL ошибка: {$error}"];
            }
            
            if ($httpCode !== 200) {
                return ['error' => "HTTP ошибка: {$httpCode}"];
            }
            
            $response = json_decode($result, true);
            
            if (!$response || !$response['ok']) {
                $errorMsg = $response['description'] ?? 'Неизвестная ошибка';
                return ['error' => "API ошибка: {$errorMsg}"];
            }
            
            return ['success' => true, 'data' => $response['result']];
        }

        // Если запрос на обновление
        if (isset($_GET['action']) && $_GET['action'] === 'refresh') {
            echo '<div class="step">';
            echo '<h3>🔄 Выполняем обновление кэша...</h3>';
            
            // 1. Проверяем бота
            echo '<div class="status info">1. Проверка бота...</div>';
            $botInfo = telegramRequest('getMe');
            if (isset($botInfo['error'])) {
                echo '<div class="status error">❌ ' . $botInfo['error'] . '</div>';
            } else {
                echo '<div class="status success">✅ Бот найден: @' . $botInfo['data']['username'] . '</div>';
                
                // 2. Удаляем webhook
                echo '<div class="status info">2. Удаление старого webhook...</div>';
                $deleteResult = telegramRequest('deleteWebhook', ['drop_pending_updates' => true]);
                if (isset($deleteResult['error'])) {
                    echo '<div class="status warning">⚠️ ' . $deleteResult['error'] . '</div>';
                } else {
                    echo '<div class="status success">✅ Webhook удален, кэш сброшен</div>';
                }
                
                // Ждем
                sleep(2);
                
                // 3. Устанавливаем новый webhook
                echo '<div class="status info">3. Установка нового webhook...</div>';
                $setResult = telegramRequest('setWebhook', [
                    'url' => $webhookUrl,
                    'allowed_updates' => ['message', 'callback_query'],
                    'drop_pending_updates' => true
                ]);
                if (isset($setResult['error'])) {
                    echo '<div class="status error">❌ ' . $setResult['error'] . '</div>';
                } else {
                    echo '<div class="status success">✅ Новый webhook установлен</div>';
                }
                
                // 4. Обновляем команды
                echo '<div class="status info">4. Обновление команд бота...</div>';
                $commands = [
                    ['command' => 'start', 'description' => 'Запустить бота и открыть мини-приложение']
                ];
                $commandsResult = telegramRequest('setMyCommands', ['commands' => $commands]);
                if (isset($commandsResult['error'])) {
                    echo '<div class="status warning">⚠️ ' . $commandsResult['error'] . '</div>';
                } else {
                    echo '<div class="status success">✅ Команды обновлены</div>';
                }
                
                // 5. Финальная проверка
                echo '<div class="status info">5. Финальная проверка...</div>';
                $finalCheck = telegramRequest('getWebhookInfo');
                if (isset($finalCheck['error'])) {
                    echo '<div class="status error">❌ ' . $finalCheck['error'] . '</div>';
                } else {
                    $webhookData = $finalCheck['data'];
                    if ($webhookData['url']) {
                        echo '<div class="status success">✅ Webhook активен: ' . $webhookData['url'] . '</div>';
                    } else {
                        echo '<div class="status error">❌ Webhook не установлен</div>';
                    }
                }
                
                echo '<div class="status success">🎉 КЭША TELEGRAM ОБНОВЛЕН!</div>';
                echo '<div class="status info">📱 Теперь попробуйте открыть @uniqpaid_paid_bot и нажать "Запустить приложение"</div>';
            }
            echo '</div>';
        } else {
            // Показываем кнопку для запуска
            echo '<div class="step">';
            echo '<h3>🚀 Готов обновить кэш Telegram?</h3>';
            echo '<p>Это действие:</p>';
            echo '<ul>';
            echo '<li>✅ Удалит старый webhook</li>';
            echo '<li>✅ Установит новый webhook</li>';
            echo '<li>✅ Сбросит кэш Telegram</li>';
            echo '<li>✅ Обновит команды бота</li>';
            echo '</ul>';
            echo '<button onclick="window.location.href=\'?action=refresh\'">🔄 ОБНОВИТЬ КЭША</button>';
            echo '</div>';
        }
        ?>

        <div class="step">
            <h3>📋 Инструкции:</h3>
            <ol>
                <li>Нажмите кнопку "Обновить кэш" выше</li>
                <li>Дождитесь завершения всех операций</li>
                <li>Откройте @uniqpaid_paid_bot в Telegram</li>
                <li>Отправьте команду /start</li>
                <li>Нажмите "Запустить приложение"</li>
                <li>Проверьте что мини-приложение загружается</li>
            </ol>
        </div>

        <div class="status warning">
            ⚠️ Если проблема остается:
            <ul>
                <li>Подождите 5-10 минут (кэш Telegram)</li>
                <li>Перезапустите Telegram</li>
                <li>Проверьте .htaccess файлы на сервере</li>
                <li>Убедитесь что X-Frame-Options установлен в ALLOWALL</li>
            </ul>
        </div>
    </div>
</body>
</html>
