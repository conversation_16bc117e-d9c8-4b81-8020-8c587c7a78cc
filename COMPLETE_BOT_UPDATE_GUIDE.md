# 🤖 Полное обновление бота @uniqpaid_paid_bot

## 📋 Информация о боте

- **Бот**: @uniqpaid_paid_bot
- **Токен**: 8105471536:AAH5hl2iouOCmnm0yj5MteqnGpziCvChcbc
- **Webhook**: https://app.uniqpaid.com/test3/bot/webhook.php
- **WebApp**: https://app.uniqpaid.com/test3/
- **API**: https://api.telegram.org/bot8105471536:AAH5hl2iouOCmnm0yj5MteqnGpziCvChcbc/

## 🚨 Проблемы которые решаем

1. **X-Frame-Options: deny** - блокирует iframe
2. **Устаревший кэш Telegram** - старые настройки
3. **Неактуальные команды бота** - нужно обновить
4. **Webhook проблемы** - требует переустановки

## 📁 Файлы для загрузки на сервер

### 1. Основные файлы (уже готовы)
- ✅ `.htaccess` - исправленный (X-Frame-Options: ALLOWALL)
- ✅ `api/.htaccess` - исправленный (CORS + iframe)
- ✅ `bot/config.php` - обновленный с правильным токеном

### 2. Скрипты обновления
- ✅ `complete_bot_update.php` - полное обновление бота
- ✅ `server_cache_refresh.php` - быстрое обновление кэша

## 🚀 Пошаговая инструкция

### Шаг 1: Загрузить файлы на сервер
```
1. Скопировать .htaccess в корень app.uniqpaid.com
2. Скопировать api/.htaccess в папку api
3. Загрузить complete_bot_update.php в корень
4. Загрузить server_cache_refresh.php в корень (резерв)
```

### Шаг 2: Исправить iframe проблему
Убедиться что в `.htaccess` есть:
```apache
# РАЗРЕШАЕМ IFRAME ДЛЯ TELEGRAM
<IfModule mod_headers.c>
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"
    Header always set Content-Security-Policy "frame-ancestors *; default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:;"
    Header always set Access-Control-Allow-Origin "*"
</IfModule>
```

### Шаг 3: Полное обновление бота
```
1. Открыть: https://app.uniqpaid.com/complete_bot_update.php
2. Нажать "🚀 ОБНОВИТЬ БОТА"
3. Дождаться завершения всех 7 шагов:
   ✅ Проверка бота
   ✅ Удаление старого webhook
   ✅ Установка нового webhook
   ✅ Обновление команд
   ✅ Настройка описания
   ✅ Короткое описание
   ✅ Финальная проверка
```

### Шаг 4: Тестирование
```
1. Открыть @uniqpaid_paid_bot в Telegram
2. Отправить /start
3. Нажать "Запустить приложение"
4. Проверить что мини-приложение загружается
5. Убедиться что отображается:
   - Баланс: 497 монет
   - История выплат: 2 карточки
   - Все разделы работают
```

## 🎯 Ожидаемый результат

После выполнения всех шагов:

### ✅ Исправлено
- ❌ "X-Frame-Options: deny" → ✅ "X-Frame-Options: ALLOWALL"
- ❌ Устаревший webhook → ✅ Новый webhook с секретным токеном
- ❌ Старые команды → ✅ Обновленные команды (/start, /help, /balance, /referral)
- ❌ Пустое описание → ✅ Полное описание бота
- ❌ Кэш проблемы → ✅ Сброшенный кэш

### ✅ Работает
- 🤖 Бот отвечает на команды
- 📱 Мини-приложение загружается в Telegram
- 💰 Отображается корректный баланс
- 📋 Показывается история выплат
- 🔄 Все API функции работают

## 🔧 Команды бота после обновления

- `/start` - 🚀 Запустить бота и открыть мини-приложение
- `/help` - ❓ Помощь по использованию бота
- `/balance` - 💰 Проверить баланс монет
- `/referral` - 👥 Реферальная программа

## 📝 Описание бота

**Полное описание:**
```
🎯 UniQPaid - зарабатывайте криптовалюту за просмотр рекламы!

💰 10 монет за каждый просмотр
🔄 Вывод в BTC, ETH, USDT, TRX
👥 Реферальная программа 10%

🚀 Нажмите /start чтобы начать!
```

**Короткое описание:**
```
💰 Зарабатывайте криптовалюту за просмотр рекламы! 10 монет за просмотр, вывод в BTC/ETH/USDT/TRX
```

## ⚠️ Важные моменты

1. **Порядок действий важен**: сначала .htaccess, потом обновление бота
2. **Время применения**: 2-3 минуты после обновления
3. **Кэш браузера**: очистить после изменений
4. **Telegram кэш**: может потребоваться перезапуск приложения

## 🆘 Если что-то не работает

### Проблема: Мини-приложение не загружается
**Решение:**
1. Проверить .htaccess файлы
2. Убедиться что X-Frame-Options: ALLOWALL
3. Перезапустить Apache/Nginx
4. Очистить кэш браузера

### Проблема: Бот не отвечает
**Решение:**
1. Проверить webhook: https://api.telegram.org/bot8105471536:AAH5hl2iouOCmnm0yj5MteqnGpziCvChcbc/getWebhookInfo
2. Запустить complete_bot_update.php еще раз
3. Проверить логи сервера

### Проблема: 404 ошибки
**Решение:**
1. Убедиться что все файлы загружены на сервер
2. Проверить права доступа к файлам
3. Проверить .htaccess правила

## 📞 Поддержка

Если проблемы остаются:
1. Проверить логи Apache: `/var/log/apache2/error.log`
2. Проверить логи бота: `bot/bot.log`
3. Связаться с хостинг-провайдером
4. Проверить настройки домена

---

**Статус**: ✅ Готово к применению на продакшн сервере
**Время выполнения**: ~10-15 минут
**Сложность**: Средняя (требует доступа к серверу)
