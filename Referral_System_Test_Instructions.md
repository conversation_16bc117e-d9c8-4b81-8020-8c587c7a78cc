# Инструкция по тестированию реферальной системы

1. Откройте админ-панель: http://argun-defolt.loc/api/admin/users.php
2. Авторизуйтесь под учетной записью администратора
3. Добавьте нового пользователя (реферер):
   - Нажмите "Добавить пользователя"
   - Введите данные:
     - ID: 1001
     - Имя пользователя: referrer_test
     - Баланс: 0
   - Нажмите "Добавить"

4. Добавьте второго пользователя (реферал):
   - Нажмите "Добавить пользователя"
   - Введите данные:
     - ID: 1002
     - Имя пользователя: referral_test
     - Баланс: 0
     - ID реферера: 1001
   - Нажмите "Добавить"

5. Обновите баланс реферала:
   - Найдите пользователя 1002 в таблице
   - В столбце "Действия" нажмите "Изменить баланс"
   - Введите новый баланс: 1000
   - Нажмите "Обновить"

6. Проверьте баланс реферера:
   - Найдите пользователя 1001 в таблице
   - Убедитесь, что его баланс составляет 100 рублей (10% от 1000)
   - Проверьте столбец "Реферальный заработок" - должно быть 100

7. Для дополнительной проверки:
   - Откройте файл `api/user_data.json`
   - Найдите пользователя 1001 и проверьте поля:
     - "referral_earnings": 100
     - "balance": 100
   - Найдите пользователя 1002 и проверьте поле:
     - "balance": 1000

Примечание: Если тест не проходит, проверьте логи в файле `api/audit.log` на наличие записей о начислении реферального бонуса.