# 🔧 Исправление системы выплат UniQPaid

## 🚨 Найденные проблемы

1. **Отсутствует `payout_id`** в сохраняемых данных выплаты
2. **Статусы не обновляются автоматически** из-за отсутствия правильного ID
3. **Монеты списываются, но статус остается `pending`**
4. **Нет автоматической синхронизации с NOWPayments**
5. **История выплат не отображается корректно в приложении**

## ✅ Исправления

### 📁 Обновленные файлы:

1. **`api/requestWithdrawal.php`** - исправлено сохранение `payout_id`
2. **`api/NOWPaymentsAPI.php`** - улучшена функция `getPayoutStatus`
3. **`api/auto_update_withdrawals.php`** - новый файл для автообновления
4. **`api/fix_withdrawals.php`** - веб-интерфейс для исправления проблем
5. **`api/setup_cron.php`** - настройка автоматизации
6. **`main.js`** - улучшено отображение истории

## 🚀 Пошаговое исправление

### Шаг 1: Загрузите файлы на сервер
Загрузите все обновленные файлы в соответствующие папки.

### Шаг 2: Исправьте существующие выплаты
Откройте: `https://app.uniqpaid.com/test2/api/fix_withdrawals.php`

**Действия:**
1. Нажмите "🆔 Исправить отсутствующие payout_id"
2. Нажмите "🔄 Обновить все статусы"
3. Нажмите "💰 Вернуть средства за неудачные выплаты"

### Шаг 3: Настройте автоматическое обновление
Откройте: `https://app.uniqpaid.com/test2/api/setup_cron.php`

**Варианты:**
- **Cron (рекомендуется):** Добавьте задание в crontab
- **Веб-вызов:** Используйте внешний сервис для вызова URL каждые 5 минут

### Шаг 4: Проверьте результат
1. Создайте тестовую выплату
2. Проверьте, что монеты списались с баланса
3. Убедитесь, что выплата появилась в истории
4. Подождите обновления статуса (5-10 минут)

## 🛠️ Веб-инструменты

### Панель исправления выплат
```
https://app.uniqpaid.com/test2/api/fix_withdrawals.php
```
- Анализ проблем
- Исправление отсутствующих ID
- Обновление статусов
- Возврат средств

### Автоматическое обновление
```
https://app.uniqpaid.com/test2/api/auto_update_withdrawals.php?web=1
```
- Проверка всех активных выплат
- Обновление статусов
- Автоматический возврат средств за неудачные выплаты

### Настройка автоматизации
```
https://app.uniqpaid.com/test2/api/setup_cron.php
```
- Инструкции по настройке cron
- Альтернативные способы автоматизации
- Проверка текущего состояния

## 🔄 Автоматизация

### Cron-задача (рекомендуется)
```bash
*/5 * * * * /usr/bin/php /path/to/api/auto_update_withdrawals.php
```

### Внешний сервис
Настройте вызов URL каждые 5 минут:
```
https://app.uniqpaid.com/test2/api/auto_update_withdrawals.php?web=1
```

## 📊 Мониторинг

### Админ-панель выплат
```
https://app.uniqpaid.com/test2/api/admin/withdrawals.php
```
- Полная статистика выплат
- Поиск и фильтрация
- Проверка статусов в реальном времени

### Логи
Проверяйте файл `error_log` на наличие ошибок:
- Успешные обновления статусов
- Ошибки API NOWPayments
- Проблемы с сохранением данных

## 🎯 Ожидаемый результат

После исправления:

1. ✅ **Монеты списываются** с баланса при создании выплаты
2. ✅ **Статусы обновляются автоматически** каждые 5 минут
3. ✅ **История выплат отображается** корректно в приложении
4. ✅ **Неудачные выплаты** автоматически возвращают средства
5. ✅ **Синхронизация с NOWPayments** работает стабильно

## 🚨 Экстренные действия

### Если что-то пошло не так:

1. **Откатите изменения** - восстановите backup файлов
2. **Проверьте логи** - найдите конкретную ошибку
3. **Запустите исправление** - используйте веб-инструменты
4. **Обратитесь за помощью** - приложите логи и описание проблемы

### Backup важных файлов:
- `api/user_data.json` - данные пользователей
- `api/config.php` - настройки
- `main.js` - интерфейс пользователя

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в `error_log`
2. Запустите диагностику через веб-интерфейс
3. Убедитесь, что API ключи NOWPayments корректны
4. Проверьте доступность сервера к api.nowpayments.io

---

**Важно:** После исправления обязательно протестируйте систему с небольшой суммой перед использованием в продакшене!
