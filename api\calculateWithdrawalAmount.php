<?php
/**
 * api/calculateWithdrawalAmount.php
 * Рассчитывает точную сумму в криптовалюте для вывода (синхронизировано с requestWithdrawal.php)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

try {
    // Получение входных данных
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);

    if (!isset($input['coins_amount']) || !isset($input['currency'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Отсутствуют обязательные параметры: coins_amount, currency']);
        exit;
    }

    $coinsAmount = intval($input['coins_amount']);
    $currency = strtolower(trim($input['currency']));

    if ($coinsAmount <= 0) {
        echo json_encode([
            'success' => true,
            'crypto_amount' => 0,
            'usd_amount' => 0,
            'usd_after_fee' => 0,
            'fee_applied' => false,
            'message' => 'Сумма должна быть больше 0'
        ]);
        exit;
    }

    // Конвертируем монеты в USD (точно так же как в requestWithdrawal.php)
    $usdAmount = $coinsAmount * CONVERSION_RATE;
    
    // Получаем данные о валюте и учитываем комиссию (точно так же как в requestWithdrawal.php)
    $usdAmountAfterFee = $usdAmount;
    $feeApplied = false;
    $networkFee = 0;
    
    if (defined('SHOW_FEES_TO_USER') && SHOW_FEES_TO_USER) {
        // Получаем актуальные данные о валютах из API
        $currencyDataResponse = file_get_contents('http://argun-defolt.loc/api/getCurrencyData.php');
        $currencyApiData = json_decode($currencyDataResponse, true);

        if ($currencyApiData && $currencyApiData['success'] && isset($currencyApiData['currencies'][$currency])) {
            $currencyData = $currencyApiData['currencies'][$currency];
            $networkFee = $currencyData['networkFee'];
            $usdAmountAfterFee = $usdAmount - $networkFee;
            $feeApplied = true;

            if ($usdAmountAfterFee <= 0) {
                echo json_encode([
                    'success' => false,
                    'error' => 'Комиссия больше суммы вывода',
                    'crypto_amount' => 0,
                    'usd_amount' => $usdAmount,
                    'usd_after_fee' => $usdAmountAfterFee,
                    'network_fee' => $networkFee,
                    'fee_applied' => $feeApplied
                ]);
                exit;
            }
        } else {
            // Fallback на статичные данные если API недоступен
            $currencyData = getCurrencyDataFallback($currency);
            if ($currencyData && isset($currencyData['networkFee'])) {
                $networkFee = $currencyData['networkFee'];
                $usdAmountAfterFee = $usdAmount - $networkFee;
                $feeApplied = true;

                if ($usdAmountAfterFee <= 0) {
                    echo json_encode([
                        'success' => false,
                        'error' => 'Комиссия больше суммы вывода',
                        'crypto_amount' => 0,
                        'usd_amount' => $usdAmount,
                        'usd_after_fee' => $usdAmountAfterFee,
                        'network_fee' => $networkFee,
                        'fee_applied' => $feeApplied
                    ]);
                    exit;
                }
            }
        }
    }

    // Создаем экземпляр API клиента
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

    // Получаем оценку суммы в выбранной криптовалюте (точно так же как в requestWithdrawal.php)
    $estimate = $api->getEstimateAmount($usdAmountAfterFee, 'usd', $currency);

    if (!isset($estimate['estimated_amount'])) {
        echo json_encode([
            'success' => false,
            'error' => 'Не удалось получить оценку суммы для конвертации',
            'crypto_amount' => 0,
            'usd_amount' => $usdAmount,
            'usd_after_fee' => $usdAmountAfterFee,
            'network_fee' => $networkFee,
            'fee_applied' => $feeApplied
        ]);
        exit;
    }

    $cryptoAmount = $estimate['estimated_amount'];

    // Возвращаем результат
    echo json_encode([
        'success' => true,
        'crypto_amount' => $cryptoAmount,
        'usd_amount' => $usdAmount,
        'usd_after_fee' => $usdAmountAfterFee,
        'network_fee' => $networkFee,
        'fee_applied' => $feeApplied,
        'currency' => $currency,
        'coins_amount' => $coinsAmount,
        'conversion_rate' => CONVERSION_RATE,
        'calculated_at' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    error_log("calculateWithdrawalAmount ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера при расчете суммы',
        'details' => $e->getMessage()
    ]);
}

/**
 * Получает данные о валюте с комиссиями (fallback версия)
 */
function getCurrencyDataFallback($currency) {
    $currencyData = [
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 250,
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 5,
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 8580,
            'networkFee' => 5.58,
            'status' => 'expensive'
        ],
        'trx' => [
            'name' => 'TRON (TRX)',
            'minCoins' => 1000,
            'networkFee' => 0.30,
            'status' => 'good'
        ]
    ];

    return isset($currencyData[$currency]) ? $currencyData[$currency] : null;
}
?>
