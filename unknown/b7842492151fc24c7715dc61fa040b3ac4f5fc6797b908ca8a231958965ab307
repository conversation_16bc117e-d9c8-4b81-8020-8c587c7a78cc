<?php
/**
 * Отладочная версия отчетов по выплатам
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../functions.php';

// Проверяем авторизацию администратора
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

header('Content-Type: text/plain; charset=utf-8');

echo "=== ОТЛАДКА ОТЧЕТОВ ПО ВЫПЛАТАМ ===\n\n";

// Загружаем данные пользователей
$userData = loadUserData();
if (!$userData) {
    echo "❌ Не удалось загрузить данные пользователей\n";
    exit;
}

echo "✅ Данные пользователей загружены\n";
echo "Всего пользователей: " . count($userData) . "\n\n";

// Параметры из GET запроса
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$currency_filter = $_GET['currency'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$user_filter = $_GET['user'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;

echo "Параметры фильтрации:\n";
echo "- Поиск: '$search'\n";
echo "- Статус: '$status_filter'\n";
echo "- Валюта: '$currency_filter'\n";
echo "- Дата от: '$date_from'\n";
echo "- Дата до: '$date_to'\n";
echo "- Пользователь: '$user_filter'\n";
echo "- Страница: $page\n\n";

// Собираем все выплаты
$allWithdrawals = [];
$currencies = [];
$statuses = [];

foreach ($userData as $userId => $user) {
    if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
        echo "Пользователь {$userId} ({$user['first_name']} {$user['last_name']}):\n";
        echo "  Выплат: " . count($user['withdrawals']) . "\n";
        
        foreach ($user['withdrawals'] as $index => $withdrawal) {
            echo "  Выплата #{$index}:\n";
            echo "    ID: " . ($withdrawal['id'] ?? 'N/A') . "\n";
            echo "    Payout ID: " . ($withdrawal['payout_id'] ?? 'N/A') . "\n";
            echo "    Статус: " . ($withdrawal['status'] ?? 'N/A') . "\n";
            echo "    Сумма: " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет\n";
            echo "    Валюта: " . ($withdrawal['currency'] ?? 'N/A') . "\n";
            echo "    Дата: " . (isset($withdrawal['timestamp']) ? date('Y-m-d H:i:s', $withdrawal['timestamp']) : 'N/A') . "\n";
            
            // Добавляем информацию о пользователе
            $withdrawal['user_id'] = $userId;
            $withdrawal['user_name'] = $user['first_name'] ?? 'Неизвестно';
            $withdrawal['user_lastname'] = $user['last_name'] ?? '';
            $withdrawal['username'] = $user['username'] ?? '';
            $withdrawal['withdrawal_index'] = $index;
            
            // Собираем уникальные валюты и статусы
            if (isset($withdrawal['currency'])) {
                $currencies[$withdrawal['currency']] = true;
            }
            if (isset($withdrawal['status'])) {
                $statuses[$withdrawal['status']] = true;
            }
            
            $allWithdrawals[] = $withdrawal;
            echo "    ✅ Добавлено в общий список\n";
        }
        echo "\n";
    }
}

echo "=== ПОСЛЕ СБОРА ДАННЫХ ===\n";
echo "Всего выплат в массиве: " . count($allWithdrawals) . "\n";
echo "Уникальных валют: " . count($currencies) . " (" . implode(', ', array_keys($currencies)) . ")\n";
echo "Уникальных статусов: " . count($statuses) . " (" . implode(', ', array_keys($statuses)) . ")\n\n";

// Сортируем по времени (новые сначала)
usort($allWithdrawals, function($a, $b) {
    return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
});

echo "=== ПОСЛЕ СОРТИРОВКИ ===\n";
echo "Выплаты отсортированы по времени\n\n";

// Применяем фильтры
$filteredWithdrawals = array_filter($allWithdrawals, function($withdrawal) use ($search, $status_filter, $currency_filter, $date_from, $date_to, $user_filter) {
    // Поиск по тексту
    if (!empty($search)) {
        $searchText = strtolower($search);
        $searchFields = [
            strtolower($withdrawal['user_name'] ?? ''),
            strtolower($withdrawal['user_lastname'] ?? ''),
            strtolower($withdrawal['username'] ?? ''),
            strtolower($withdrawal['user_id'] ?? ''),
            strtolower($withdrawal['wallet_address'] ?? $withdrawal['address'] ?? ''),
            strtolower($withdrawal['currency'] ?? ''),
            strtolower($withdrawal['payout_id'] ?? ''),
            strtolower($withdrawal['id'] ?? ''),
            strtolower($withdrawal['transaction_hash'] ?? ''),
        ];
        
        $found = false;
        foreach ($searchFields as $field) {
            if (strpos($field, $searchText) !== false) {
                $found = true;
                break;
            }
        }
        if (!$found) return false;
    }
    
    // Фильтр по статусу
    if (!empty($status_filter) && ($withdrawal['status'] ?? '') !== $status_filter) {
        return false;
    }
    
    // Фильтр по валюте
    if (!empty($currency_filter) && ($withdrawal['currency'] ?? '') !== $currency_filter) {
        return false;
    }
    
    // Фильтр по пользователю
    if (!empty($user_filter) && ($withdrawal['user_id'] ?? '') !== $user_filter) {
        return false;
    }
    
    // Фильтр по дате
    if (!empty($date_from)) {
        $from_timestamp = strtotime($date_from);
        if (($withdrawal['timestamp'] ?? 0) < $from_timestamp) {
            return false;
        }
    }
    
    if (!empty($date_to)) {
        $to_timestamp = strtotime($date_to . ' 23:59:59');
        if (($withdrawal['timestamp'] ?? 0) > $to_timestamp) {
            return false;
        }
    }
    
    return true;
});

echo "=== ПОСЛЕ ФИЛЬТРАЦИИ ===\n";
echo "Выплат после фильтрации: " . count($filteredWithdrawals) . "\n\n";

// Пагинация
$total_withdrawals = count($filteredWithdrawals);
$total_pages = ceil($total_withdrawals / $per_page);
$offset = ($page - 1) * $per_page;
$withdrawals_page = array_slice($filteredWithdrawals, $offset, $per_page);

echo "=== ПАГИНАЦИЯ ===\n";
echo "Всего выплат: {$total_withdrawals}\n";
echo "Всего страниц: {$total_pages}\n";
echo "Текущая страница: {$page}\n";
echo "Записей на странице: " . count($withdrawals_page) . "\n\n";

echo "=== ВЫПЛАТЫ НА ТЕКУЩЕЙ СТРАНИЦЕ ===\n";
foreach ($withdrawals_page as $i => $withdrawal) {
    echo ($i + 1) . ". ";
    echo "Пользователь: {$withdrawal['user_name']} ({$withdrawal['user_id']}) | ";
    echo "Payout ID: " . ($withdrawal['payout_id'] ?? $withdrawal['id'] ?? 'N/A') . " | ";
    echo "Статус: {$withdrawal['status']} | ";
    echo "Сумма: {$withdrawal['coins_amount']} монет | ";
    echo "Валюта: {$withdrawal['currency']} | ";
    echo "Дата: " . (isset($withdrawal['timestamp']) ? date('Y-m-d H:i:s', $withdrawal['timestamp']) : 'N/A') . "\n";
}

echo "\n=== ОТЛАДКА ЗАВЕРШЕНА ===\n";
?>
