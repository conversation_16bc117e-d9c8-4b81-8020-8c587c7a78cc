<?php
/**
 * Тест для проверки истории выплат пользователя
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/functions.php';

header('Content-Type: text/plain; charset=utf-8');

echo "=== ТЕСТ ИСТОРИИ ВЫПЛАТ ===\n\n";

// ID пользователя с выплатами
$testUserId = 5880288830;

echo "Тестируем пользователя ID: {$testUserId}\n\n";

// 1. Проверяем данные напрямую из файла
echo "1. ПРОВЕРКА ДАННЫХ ИЗ ФАЙЛА:\n";
$userData = loadUserData();
if (!$userData) {
    echo "❌ Не удалось загрузить данные пользователей\n";
    exit;
}

if (!isset($userData[$testUserId])) {
    echo "❌ Пользователь {$testUserId} не найден\n";
    exit;
}

$user = $userData[$testUserId];
echo "✅ Пользователь найден: {$user['first_name']} {$user['last_name']}\n";
echo "Баланс: {$user['balance']} монет\n";

if (isset($user['withdrawals'])) {
    echo "Выплат в данных: " . count($user['withdrawals']) . "\n\n";
    
    foreach ($user['withdrawals'] as $index => $withdrawal) {
        echo "Выплата #{$index}:\n";
        echo "  ID: " . ($withdrawal['id'] ?? 'N/A') . "\n";
        echo "  Payout ID: " . ($withdrawal['payout_id'] ?? 'N/A') . "\n";
        echo "  Статус: " . ($withdrawal['status'] ?? 'N/A') . "\n";
        echo "  Сумма: " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет\n";
        echo "  Валюта: " . ($withdrawal['currency'] ?? 'N/A') . "\n";
        echo "  Адрес: " . ($withdrawal['wallet_address'] ?? $withdrawal['address'] ?? 'N/A') . "\n";
        echo "  Дата создания: " . ($withdrawal['created_at'] ?? 'N/A') . "\n";
        echo "  Timestamp: " . ($withdrawal['timestamp'] ?? 'N/A') . "\n";
        if (isset($withdrawal['timestamp'])) {
            echo "  Дата (читаемая): " . date('Y-m-d H:i:s', $withdrawal['timestamp']) . "\n";
        }
        echo "\n";
    }
} else {
    echo "❌ У пользователя нет поля 'withdrawals'\n";
}

// 2. Тестируем API getWithdrawalHistory.php
echo "\n2. ТЕСТ API getWithdrawalHistory.php:\n";

// Создаем фейковые данные initData для тестирования
$fakeInitData = "user=" . urlencode(json_encode([
    'id' => $testUserId,
    'first_name' => $user['first_name'],
    'last_name' => $user['last_name'],
    'username' => $user['username'] ?? '',
    'language_code' => 'ru'
])) . "&chat_instance=test&chat_type=private&auth_date=" . time() . "&hash=test";

echo "Отправляем запрос к API...\n";

$postData = json_encode(['initData' => $fakeInitData]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData
    ]
]);

// Временно отключаем валидацию для тестирования
$originalValidation = file_get_contents(__DIR__ . '/validate_initdata.php');
$testValidation = '<?php
function validateInitData($initData) {
    $data = [];
    parse_str($initData, $data);
    if (isset($data["user"])) {
        return ["user" => json_decode($data["user"], true)];
    }
    return false;
}
?>';

file_put_contents(__DIR__ . '/validate_initdata_backup.php', $originalValidation);
file_put_contents(__DIR__ . '/validate_initdata.php', $testValidation);

try {
    $response = file_get_contents('http://argun-defolt.loc/api/getWithdrawalHistory.php', false, $context);
    
    echo "Ответ API:\n";
    echo $response . "\n\n";
    
    $decoded = json_decode($response, true);
    if ($decoded) {
        echo "Декодированный ответ:\n";
        if (isset($decoded['withdrawals'])) {
            echo "Количество выплат: " . count($decoded['withdrawals']) . "\n";
            foreach ($decoded['withdrawals'] as $i => $withdrawal) {
                echo "Выплата " . ($i + 1) . ":\n";
                echo "  Сумма: " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет\n";
                echo "  Статус: " . ($withdrawal['status'] ?? 'N/A') . "\n";
                echo "  Валюта: " . ($withdrawal['currency'] ?? 'N/A') . "\n";
                echo "  Дата: " . (isset($withdrawal['timestamp']) ? date('Y-m-d H:i:s', $withdrawal['timestamp']) : 'N/A') . "\n";
            }
        } else {
            echo "❌ Нет поля 'withdrawals' в ответе\n";
        }
        
        if (isset($decoded['error'])) {
            echo "❌ Ошибка в ответе: " . $decoded['error'] . "\n";
        }
    } else {
        echo "❌ Не удалось декодировать JSON ответ\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка запроса: " . $e->getMessage() . "\n";
} finally {
    // Восстанавливаем оригинальную валидацию
    file_put_contents(__DIR__ . '/validate_initdata.php', $originalValidation);
    unlink(__DIR__ . '/validate_initdata_backup.php');
}

// 3. Проверяем функцию getUserWithdrawals
echo "\n3. ТЕСТ ФУНКЦИИ getUserWithdrawals():\n";
$withdrawals = getUserWithdrawals($testUserId);
echo "Функция вернула " . count($withdrawals) . " выплат\n";

foreach ($withdrawals as $i => $withdrawal) {
    echo "Выплата " . ($i + 1) . ":\n";
    echo "  Сумма: " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет\n";
    echo "  Статус: " . ($withdrawal['status'] ?? 'N/A') . "\n";
    echo "  Валюта: " . ($withdrawal['currency'] ?? 'N/A') . "\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
