<?php
/**
 * update_webhook.php
 * Скрипт для обновления webhook Telegram бота
 */

require_once 'config.php';

echo "<h2>🔄 Обновление Webhook для Telegram бота</h2>\n";

// Сначала удаляем старый webhook
echo "<p>🗑️ Удаляем старый webhook...</p>\n";
$deleteResult = deleteWebhook();

if ($deleteResult) {
    echo "<p style='color: green;'>✅ Старый webhook успешно удален</p>\n";
} else {
    echo "<p style='color: orange;'>⚠️ Не удалось удалить старый webhook (возможно, его не было)</p>\n";
}

// Ждем немного
sleep(2);

// Устанавливаем новый webhook
echo "<p>🔧 Устанавливаем новый webhook...</p>\n";
echo "<p>📍 URL: " . WEBHOOK_URL . "</p>\n";

$setResult = setWebhook();

if ($setResult) {
    echo "<p style='color: green;'>✅ Новый webhook успешно установлен!</p>\n";
    echo "<p>🎯 Бот готов к работе с новыми настройками</p>\n";
    
    // Проверяем информацию о webhook
    echo "<h3>📋 Информация о webhook:</h3>\n";
    $webhookInfo = telegramRequest('getWebhookInfo');
    
    if ($webhookInfo) {
        echo "<ul>\n";
        echo "<li><strong>URL:</strong> " . ($webhookInfo['url'] ?? 'Не установлен') . "</li>\n";
        echo "<li><strong>Статус:</strong> " . ($webhookInfo['has_custom_certificate'] ? 'С сертификатом' : 'Без сертификата') . "</li>\n";
        echo "<li><strong>Ожидающих обновлений:</strong> " . ($webhookInfo['pending_update_count'] ?? 0) . "</li>\n";
        echo "<li><strong>Последняя ошибка:</strong> " . ($webhookInfo['last_error_message'] ?? 'Нет ошибок') . "</li>\n";
        echo "</ul>\n";
    }
    
} else {
    echo "<p style='color: red;'>❌ Ошибка при установке webhook!</p>\n";
    echo "<p>Проверьте:</p>\n";
    echo "<ul>\n";
    echo "<li>Правильность URL в config.php</li>\n";
    echo "<li>Доступность домена из интернета</li>\n";
    echo "<li>SSL сертификат</li>\n";
    echo "<li>Токен бота</li>\n";
    echo "</ul>\n";
}

echo "<hr>\n";
echo "<p><a href='../'>🏠 Вернуться к приложению</a></p>\n";
echo "<p><a href='../admin/'>⚙️ Админ панель</a></p>\n";
?>
