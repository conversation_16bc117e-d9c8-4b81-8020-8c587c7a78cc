# 🔧 Устранение проблем с Telegram Bot

## 🚨 Текущие ошибки в логах

```
[2025-05-26 19:07:37] ERROR: Не удалось отправить запрос к Telegram API: setWebhook
[2025-05-26 22:48:46] ERROR: Не удалось отправить запрос к Telegram API: getMe
[2025-05-26 22:52:46] TEST: Тестовое сообщение
```

## 🎯 Быстрое решение

### 1. Автоматическое исправление
Откройте в браузере:
```
https://app.uniqpaid.com/test3/bot/fix_bot.php
```

Этот инструмент автоматически:
- ✅ Проверит связь с Telegram API
- ✅ Переустановит webhook
- ✅ Очистит старые ошибки
- ✅ Протестирует отправку сообщений

### 2. Подробная диагностика
Если автоматическое исправление не помогло:
```
https://app.uniqpaid.com/test3/bot/diagnose.php
```

## 🔍 Возможные причины ошибок

### Ошибка "Не удалось отправить запрос к Telegram API"

**Причина 1: Проблемы с интернет-соединением**
- Сервер не может подключиться к api.telegram.org
- Блокировка портов или файрвол

**Причина 2: Неверный BOT_TOKEN**
- Токен изменился или стал недействительным
- Опечатка в токене

**Причина 3: Проблемы с SSL/TLS**
- Устаревшие SSL сертификаты
- Проблемы с cURL

**Причина 4: Превышение лимитов**
- Слишком много запросов к API
- Временная блокировка

## 🛠️ Пошаговое устранение

### Шаг 1: Проверка токена
1. Откройте `bot/config.php`
2. Убедитесь, что `BOT_TOKEN` правильный:
   ```php
   define('BOT_TOKEN', '8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA');
   ```
3. Если сомневаетесь, получите новый токен у @BotFather

### Шаг 2: Проверка webhook URL
1. Убедитесь, что URL правильный:
   ```php
   define('WEBHOOK_URL', 'https://app.uniqpaid.com/test3/bot/webhook.php');
   ```
2. Проверьте доступность URL в браузере

### Шаг 3: Переустановка webhook
```
https://app.uniqpaid.com/test3/bot/set_webhook.php
```

### Шаг 4: Очистка и тест
1. Очистите логи
2. Протестируйте бота командой `/start`

## 🔧 Ручное исправление

### Если автоматические инструменты не работают

**1. Проверка через cURL в терминале:**
```bash
curl -X POST "https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/getMe"
```

**2. Удаление webhook:**
```bash
curl -X POST "https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/deleteWebhook"
```

**3. Установка webhook:**
```bash
curl -X POST "https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/setWebhook" \
     -d "url=https://app.uniqpaid.com/test3/bot/webhook.php"
```

## 📋 Проверочный список

### ✅ Что должно работать:
- [ ] `getMe` возвращает информацию о боте
- [ ] `setWebhook` устанавливается без ошибок
- [ ] `getWebhookInfo` показывает активный webhook
- [ ] Бот отвечает на команду `/start`
- [ ] Картинка отправляется в приветственном сообщении

### ❌ Признаки проблем:
- [ ] Ошибки в логах при запросах к API
- [ ] Webhook не устанавливается
- [ ] Бот не отвечает на сообщения
- [ ] Картинка не отправляется

## 🆘 Экстренные меры

### Если ничего не помогает:

**1. Создайте нового бота:**
- Обратитесь к @BotFather
- Создайте нового бота командой `/newbot`
- Замените токен в `config.php`

**2. Проверьте сервер:**
- Убедитесь, что сервер имеет доступ к интернету
- Проверьте настройки файрвола
- Убедитесь, что PHP имеет доступ к cURL

**3. Альтернативный webhook:**
- Используйте ngrok для локального тестирования
- Попробуйте другой домен для webhook

## 📞 Поддержка

### Полезные ссылки:
- [Telegram Bot API Documentation](https://core.telegram.org/bots/api)
- [BotFather](https://t.me/botfather)
- [Telegram Bot Support](https://t.me/BotSupport)

### Логи для анализа:
Всегда прикладывайте содержимое файла `bot/bot.log` при обращении за помощью.

### Тестовые команды:
```
/start - основная команда
/help - помощь
/balance - баланс пользователя
```

## 🎯 После исправления

1. ✅ Протестируйте все функции бота
2. ✅ Проверьте отправку картинки
3. ✅ Убедитесь, что webhook стабильно работает
4. ✅ Мониторьте логи на предмет новых ошибок
