<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#avatarGradient)" filter="url(#glow)"/>
  
  <!-- Inner circle -->
  <circle cx="256" cy="256" r="200" fill="none" stroke="#FFFFFF" stroke-width="4" opacity="0.3"/>
  
  <!-- Coin symbol -->
  <circle cx="256" cy="256" r="120" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="256" cy="256" r="100" fill="url(#avatarGradient)"/>
  
  <!-- Dollar sign -->
  <path d="M256 180 L256 160 M256 360 L256 340 M220 200 Q220 180 240 180 L280 180 Q300 180 300 200 Q300 220 280 220 L240 220 Q220 220 220 240 Q220 260 240 260 L280 260 Q300 260 300 280 Q300 300 280 300 L240 300 Q220 300 220 320" 
        stroke="#FFFFFF" stroke-width="12" fill="none" stroke-linecap="round"/>
  
  <!-- Decorative elements -->
  <circle cx="180" cy="180" r="8" fill="#FFFFFF" opacity="0.6"/>
  <circle cx="332" cy="180" r="8" fill="#FFFFFF" opacity="0.6"/>
  <circle cx="180" cy="332" r="8" fill="#FFFFFF" opacity="0.6"/>
  <circle cx="332" cy="332" r="8" fill="#FFFFFF" opacity="0.6"/>
</svg>
