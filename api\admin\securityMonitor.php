<?php
/**
 * api/admin/securityMonitor.php
 * Административный интерфейс для мониторинга безопасности и подозрительной активности
 * 
 * ВАЖНО: Этот файл должен быть защищен паролем или другими методами аутентификации!
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Базовая защита паролем (в реальном проекте нужно использовать более надежную аутентификацию)
$adminPassword = 'your_secure_password'; // Замените на надежный пароль

// Проверка аутентификации
session_start();

if (!isset($_SESSION['admin_authenticated'])) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['password'])) {
        if ($_POST['password'] === $adminPassword) {
            $_SESSION['admin_authenticated'] = true;
        } else {
            echo "Неверный пароль";
            exit;
        }
    } else {
        // Показываем форму входа
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Вход в панель безопасности</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .login-form { max-width: 400px; margin: 50px auto; padding: 20px; background: white; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { text-align: center; color: #333; }
                input[type="password"] { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 3px; }
                button { width: 100%; padding: 10px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; }
                button:hover { background: #45a049; }
            </style>
        </head>
        <body>
            <div class="login-form">
                <h1>Вход в панель безопасности</h1>
                <form method="post">
                    <input type="password" name="password" placeholder="Введите пароль" required>
                    <button type="submit">Войти</button>
                </form>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in securityMonitor.php'); 
    echo 'Ошибка: Не удалось загрузить config.php'; 
    exit; 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in securityMonitor.php'); 
    echo 'Ошибка: Не удалось загрузить db_mock.php'; 
    exit; 
}
if (!(@require_once __DIR__ . '/../security.php')) { 
    http_response_code(500); 
    error_log('FATAL: security.php not found in securityMonitor.php'); 
    echo 'Ошибка: Не удалось загрузить security.php'; 
    exit; 
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    echo 'Ошибка: Не удалось загрузить данные пользователей';
    exit;
}

// Обработка действий
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && isset($_POST['user_id'])) {
        $userId = intval($_POST['user_id']);
        
        switch ($_POST['action']) {
            case 'unblock':
                if (isset($userData[$userId])) {
                    $userData[$userId]['blocked'] = false;
                    $userData[$userId]['suspicious_activity'] = 0;
                    saveUserData($userData);
                    logAuditEvent('admin_unblock_user', $userId, ['admin_ip' => $_SERVER['REMOTE_ADDR']]);
                    echo "<div style='color: green; padding: 10px; background: #e8f5e9; margin-bottom: 20px; border-radius: 3px;'>Пользователь $userId разблокирован</div>";
                }
                break;
                
            case 'block':
                if (isset($userData[$userId])) {
                    $userData[$userId]['blocked'] = true;
                    $userData[$userId]['blocked_at'] = time();
                    saveUserData($userData);
                    logAuditEvent('admin_block_user', $userId, ['admin_ip' => $_SERVER['REMOTE_ADDR']]);
                    echo "<div style='color: green; padding: 10px; background: #e8f5e9; margin-bottom: 20px; border-radius: 3px;'>Пользователь $userId заблокирован</div>";
                }
                break;
                
            case 'reset_suspicious':
                if (isset($userData[$userId])) {
                    $userData[$userId]['suspicious_activity'] = 0;
                    saveUserData($userData);
                    logAuditEvent('admin_reset_suspicious', $userId, ['admin_ip' => $_SERVER['REMOTE_ADDR']]);
                    echo "<div style='color: green; padding: 10px; background: #e8f5e9; margin-bottom: 20px; border-radius: 3px;'>Счетчик подозрительной активности для пользователя $userId сброшен</div>";
                }
                break;
        }
    }
}

// Получение журнала аудита
$auditLog = [];
$auditLogFile = __DIR__ . '/../audit.log';
if (file_exists($auditLogFile)) {
    $auditLog = array_reverse(file($auditLogFile));
}

// Фильтрация пользователей с подозрительной активностью
$suspiciousUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0) {
        $suspiciousUsers[$userId] = $user;
    }
}

// Фильтрация заблокированных пользователей
$blockedUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['blocked']) && $user['blocked']) {
        $blockedUsers[$userId] = $user;
    }
}

// Вывод HTML страницы
?>
<!DOCTYPE html>
<html>
<head>
    <title>Панель мониторинга безопасности</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        h1, h2 { color: #333; }
        .card { background: white; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 20px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        tr:hover { background-color: #f5f5f5; }
        .btn { padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; color: white; }
        .btn-danger { background-color: #f44336; }
        .btn-success { background-color: #4CAF50; }
        .btn-warning { background-color: #ff9800; }
        .log-entry { font-family: monospace; font-size: 12px; margin-bottom: 5px; }
        .tab { overflow: hidden; border: 1px solid #ccc; background-color: #f1f1f1; border-radius: 3px 3px 0 0; }
        .tab button { background-color: inherit; float: left; border: none; outline: none; cursor: pointer; padding: 14px 16px; transition: 0.3s; }
        .tab button:hover { background-color: #ddd; }
        .tab button.active { background-color: #ccc; }
        .tabcontent { display: none; padding: 20px; border: 1px solid #ccc; border-top: none; border-radius: 0 0 3px 3px; }
        .active-tab { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Панель мониторинга безопасности</h1>
        
        <div class="tab">
            <button class="tablinks active" onclick="openTab(event, 'suspicious')">Подозрительная активность</button>
            <button class="tablinks" onclick="openTab(event, 'blocked')">Заблокированные пользователи</button>
            <button class="tablinks" onclick="openTab(event, 'audit')">Журнал аудита</button>
        </div>
        
        <div id="suspicious" class="tabcontent active-tab">
            <h2>Пользователи с подозрительной активностью (<?php echo count($suspiciousUsers); ?>)</h2>
            <?php if (empty($suspiciousUsers)): ?>
                <p>Нет пользователей с подозрительной активностью</p>
            <?php else: ?>
                <table>
                    <tr>
                        <th>ID пользователя</th>
                        <th>Уровень подозрительности</th>
                        <th>Баланс</th>
                        <th>Действия</th>
                    </tr>
                    <?php foreach ($suspiciousUsers as $userId => $user): ?>
                        <tr>
                            <td><?php echo $userId; ?></td>
                            <td><?php echo $user['suspicious_activity']; ?></td>
                            <td><?php echo $user['balance']; ?></td>
                            <td>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                    <input type="hidden" name="action" value="block">
                                    <button type="submit" class="btn btn-danger">Заблокировать</button>
                                </form>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                    <input type="hidden" name="action" value="reset_suspicious">
                                    <button type="submit" class="btn btn-warning">Сбросить счетчик</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            <?php endif; ?>
        </div>
        
        <div id="blocked" class="tabcontent">
            <h2>Заблокированные пользователи (<?php echo count($blockedUsers); ?>)</h2>
            <?php if (empty($blockedUsers)): ?>
                <p>Нет заблокированных пользователей</p>
            <?php else: ?>
                <table>
                    <tr>
                        <th>ID пользователя</th>
                        <th>Дата блокировки</th>
                        <th>Баланс</th>
                        <th>Действия</th>
                    </tr>
                    <?php foreach ($blockedUsers as $userId => $user): ?>
                        <tr>
                            <td><?php echo $userId; ?></td>
                            <td><?php echo isset($user['blocked_at']) ? date('Y-m-d H:i:s', $user['blocked_at']) : 'Неизвестно'; ?></td>
                            <td><?php echo $user['balance']; ?></td>
                            <td>
                                <form method="post">
                                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                    <input type="hidden" name="action" value="unblock">
                                    <button type="submit" class="btn btn-success">Разблокировать</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            <?php endif; ?>
        </div>
        
        <div id="audit" class="tabcontent">
            <h2>Журнал аудита (последние 100 записей)</h2>
            <?php if (empty($auditLog)): ?>
                <p>Журнал аудита пуст</p>
            <?php else: ?>
                <div style="max-height: 500px; overflow-y: auto;">
                    <?php 
                    $count = 0;
                    foreach ($auditLog as $logEntry): 
                        if ($count++ >= 100) break;
                    ?>
                        <div class="log-entry"><?php echo htmlspecialchars($logEntry); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
                tabcontent[i].classList.remove("active-tab");
            }
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            document.getElementById(tabName).classList.add("active-tab");
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
