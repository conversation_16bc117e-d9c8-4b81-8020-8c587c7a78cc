/* ======================================== */
/* VIBRANT CYBERPUNK EFFECTS - Яркие киберпанк эффекты */
/* ======================================== */

// Инициализация ярких киберпанк эффектов
function initVibrantCyberpunkEffects() {
  console.log('🔥 Инициализация ярких киберпанк эффектов...');
  
  // Добавляем яркие визуальные эффекты
  addVibrantVisualEffects();
  
  // Создаем яркие интерактивные эффекты
  addVibrantInteractions();
  
  // Добавляем динамические анимации
  addDynamicAnimations();
  
  // НЕ ТРОГАЕМ навигацию - она работает в main.js
  
  console.log('✨ Яркие киберпанк эффекты активированы!');
}

// Яркие визуальные эффекты
function addVibrantVisualEffects() {
  // Создаем яркие плавающие частицы
  createVibrantParticles();
  
  // Добавляем яркое свечение к важным элементам
  addVibrantGlow();
  
  // Создаем эффект энергетических вспышек
  createEnergyBursts();
  
  // Добавляем радужные эффекты
  addRainbowEffects();
}

// Создание ярких плавающих частиц
function createVibrantParticles() {
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'vibrant-particles';
  particlesContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
  `;
  document.body.appendChild(particlesContainer);
  
  // Создаем 15 ярких частиц
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00', '#ff4500'];
  
  for (let i = 0; i < 15; i++) {
    const particle = document.createElement('div');
    const color = colors[i % colors.length];
    
    particle.style.cssText = `
      position: absolute;
      width: 3px;
      height: 3px;
      background: ${color};
      border-radius: 50%;
      box-shadow: 0 0 10px ${color}, 0 0 20px ${color};
      animation: vibrant-float ${3 + Math.random() * 4}s ease-in-out infinite;
      left: ${Math.random() * 100}%;
      top: ${Math.random() * 100}%;
      animation-delay: ${Math.random() * 4}s;
    `;
    
    particlesContainer.appendChild(particle);
  }
}

// Добавление яркого свечения
function addVibrantGlow() {
  // Добавляем радужное свечение к балансу при изменении
  const balanceElements = document.querySelectorAll('.balance-amount');
  balanceElements.forEach(element => {
    const observer = new MutationObserver(() => {
      element.style.animation = 'vibrant-rainbow 2s ease-in-out';
      setTimeout(() => {
        element.style.animation = 'vibrant-pulse 2s ease-in-out infinite';
      }, 2000);
    });
    
    observer.observe(element, { childList: true, characterData: true, subtree: true });
  });
  
  // Добавляем яркую пульсацию к статус сообщениям
  const statusMessages = document.querySelectorAll('.status-message');
  statusMessages.forEach(message => {
    message.style.animation = 'vibrant-glow 3s ease-in-out infinite';
  });
}

// Создание энергетических вспышек
function createEnergyBursts() {
  const burstsContainer = document.createElement('div');
  burstsContainer.className = 'energy-bursts';
  burstsContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  `;
  document.body.appendChild(burstsContainer);
  
  // Создаем случайные энергетические вспышки
  setInterval(() => {
    if (Math.random() < 0.3) { // 30% шанс каждые 3 секунды
      createEnergyBurst(burstsContainer);
    }
  }, 3000);
}

// Создание одной энергетической вспышки
function createEnergyBurst(container) {
  const burst = document.createElement('div');
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  
  burst.style.cssText = `
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom, ${color}, transparent);
    left: ${Math.random() * 100}%;
    top: -100px;
    animation: energy-burst 2s linear;
    box-shadow: 0 0 10px ${color};
  `;
  
  container.appendChild(burst);
  
  setTimeout(() => {
    burst.remove();
  }, 2000);
}

// Добавление радужных эффектов
function addRainbowEffects() {
  // Добавляем радужный эффект к заголовкам
  const headers = document.querySelectorAll('h2, h3');
  headers.forEach(header => {
    header.style.animation = 'vibrant-glow 4s ease-in-out infinite';
  });
  
  // Добавляем радужный эффект к иконкам
  const icons = document.querySelectorAll('.user-avatar-icon');
  icons.forEach(icon => {
    icon.style.animation = 'vibrant-rainbow 4s linear infinite';
  });
}

// Яркие интерактивные эффекты
function addVibrantInteractions() {
  // Яркие эффекты для кнопок
  const buttons = document.querySelectorAll('.action-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      createVibrantRipple(button);
      button.style.animation = 'vibrant-float 2s ease-in-out infinite';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.animation = '';
    });
    
    button.addEventListener('click', () => {
      createVibrantExplosion(button);
    });
  });
  
  // Яркие эффекты для карточек
  const cards = document.querySelectorAll('.app-section, .friends-block, .earn-block');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.borderColor = '#ff00ff';
      card.style.boxShadow = '0 0 30px #ff00ff';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.borderColor = '#00ffff';
      card.style.boxShadow = '0 0 20px #00ffff';
    });
  });
  
  // Яркие эффекты для полей ввода
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    input.addEventListener('focus', () => {
      input.style.boxShadow = '0 0 20px #ff00ff, 0 0 40px #ff00ff';
      input.style.borderColor = '#ff00ff';
    });
    
    input.addEventListener('blur', () => {
      input.style.boxShadow = '';
      input.style.borderColor = '#00ffff';
    });
  });
}

// Создание яркого волнового эффекта
function createVibrantRipple(element) {
  const ripple = document.createElement('div');
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: ${color};
    transform: scale(0);
    animation: vibrant-ripple 0.8s linear;
    left: 50%;
    top: 50%;
    width: 30px;
    height: 30px;
    margin-left: -15px;
    margin-top: -15px;
    pointer-events: none;
    box-shadow: 0 0 20px ${color};
  `;
  
  element.style.position = 'relative';
  element.appendChild(ripple);
  
  setTimeout(() => {
    ripple.remove();
  }, 800);
}

// Эффект яркого взрыва
function createVibrantExplosion(element) {
  const explosion = document.createElement('div');
  explosion.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    margin-left: -50px;
    margin-top: -50px;
    background: radial-gradient(circle, #ffff00, #ff00ff, transparent);
    border-radius: 50%;
    animation: vibrant-explosion 0.6s ease-out;
    pointer-events: none;
  `;
  
  element.style.position = 'relative';
  element.appendChild(explosion);
  
  setTimeout(() => {
    explosion.remove();
  }, 600);
}

// Динамические анимации
function addDynamicAnimations() {
  // Плавное появление элементов с яркими эффектами
  const elements = document.querySelectorAll('.app-section, .action-button');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.style.animation = 'vibrant-fade-in 0.8s ease forwards';
          entry.target.style.opacity = '1';
          
          // Добавляем случайный цветовой эффект
          const colors = ['#00ffff', '#ff00ff', '#00ff00'];
          const color = colors[Math.floor(Math.random() * colors.length)];
          entry.target.style.borderColor = color;
        }, index * 150);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  
  elements.forEach(element => {
    element.style.opacity = '0';
    observer.observe(element);
  });
}

// Яркие уведомления
function showVibrantNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `vibrant-notification vibrant-notification-${type}`;
  
  let borderColor = '#00ffff';
  let glowColor = '#00ffff';
  
  if (type === 'success') {
    borderColor = '#00ff00';
    glowColor = '#00ff00';
  } else if (type === 'error') {
    borderColor = '#ff0040';
    glowColor = '#ff0040';
  }
  
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(15px);
    border: 2px solid ${borderColor};
    border-radius: 15px;
    padding: 16px 20px;
    color: ${borderColor};
    font-weight: 600;
    font-size: 14px;
    z-index: 10000;
    box-shadow: 0 0 20px ${glowColor}, 0 0 40px ${glowColor};
    transform: translateX(100%) scale(0.8);
    opacity: 0;
    transition: all 0.4s ease;
    max-width: 300px;
    text-shadow: 0 0 10px ${glowColor};
    animation: vibrant-glow 2s ease-in-out infinite;
  `;
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  // Анимация появления
  setTimeout(() => {
    notification.style.transform = 'translateX(0) scale(1)';
    notification.style.opacity = '1';
  }, 100);
  
  // Автоматическое скрытие
  setTimeout(() => {
    notification.style.transform = 'translateX(100%) scale(0.8)';
    notification.style.opacity = '0';
    setTimeout(() => {
      notification.remove();
    }, 400);
  }, 4000);
}

// Добавление CSS анимаций
function addVibrantCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes vibrant-ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
    
    @keyframes vibrant-explosion {
      0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
    }
    
    @keyframes energy-burst {
      0% { top: -100px; opacity: 1; }
      100% { top: 100vh; opacity: 0; }
    }
    
    /* Улучшенные переходы */
    * {
      transition-timing-function: ease !important;
    }
    
    /* Плавная прокрутка */
    html {
      scroll-behavior: smooth;
    }
    
    /* Яркий фокус */
    button:focus,
    input:focus,
    select:focus {
      outline: 2px solid #ff00ff;
      outline-offset: 2px;
      box-shadow: 0 0 20px #ff00ff !important;
    }
    
    /* Скрытие частиц на слабых устройствах */
    @media (max-width: 768px) {
      .vibrant-particles,
      .energy-bursts {
        display: none;
      }
    }
    
    /* Уменьшение анимаций на мобильных */
    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  `;
  document.head.appendChild(style);
}

// Яркий курсор (только для десктопа)
function initVibrantCursor() {
  if (window.innerWidth <= 768) return;
  
  const cursor = document.createElement('div');
  cursor.style.cssText = `
    position: fixed;
    width: 10px;
    height: 10px;
    background: #00ffff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s ease;
    mix-blend-mode: difference;
    box-shadow: 0 0 15px #00ffff, 0 0 30px #00ffff;
    animation: vibrant-rainbow 2s linear infinite;
  `;
  document.body.appendChild(cursor);
  
  document.addEventListener('mousemove', (e) => {
    cursor.style.left = e.clientX - 5 + 'px';
    cursor.style.top = e.clientY - 5 + 'px';
  });
  
  // Яркие эффекты при наведении
  const interactiveElements = document.querySelectorAll('button, a, input, [role="button"]');
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      cursor.style.transform = 'scale(2)';
      cursor.style.background = '#ff00ff';
      cursor.style.boxShadow = '0 0 20px #ff00ff, 0 0 40px #ff00ff';
    });
    
    element.addEventListener('mouseleave', () => {
      cursor.style.transform = 'scale(1)';
      cursor.style.background = '#00ffff';
      cursor.style.boxShadow = '0 0 15px #00ffff, 0 0 30px #00ffff';
    });
  });
}

// Инициализация всех ярких эффектов
document.addEventListener('DOMContentLoaded', () => {
  // Небольшая задержка чтобы не конфликтовать с main.js
  setTimeout(() => {
    addVibrantCSS();
    initVibrantCyberpunkEffects();
    
    // Яркий курсор только для десктопа
    if (window.innerWidth > 768) {
      initVibrantCursor();
    }
    
    // Показываем яркое приветственное уведомление
    setTimeout(() => {
      showVibrantNotification('🔥 Яркий киберпанк активирован!', 'success');
    }, 1000);
  }, 300);
});

// Экспорт функций для использования в других скриптах
window.VibrantCyberpunkEffects = {
  showVibrantNotification,
  createVibrantRipple,
  createVibrantExplosion,
  initVibrantCursor
};
