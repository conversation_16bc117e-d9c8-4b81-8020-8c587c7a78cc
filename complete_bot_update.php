<?php
/**
 * Полное обновление бота @uniqpaid_paid_bot
 */

header('Content-Type: text/html; charset=utf-8');

// Конфигурация бота @uniqpaid_paid_bot
$botToken = '8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA';
$botUsername = 'uniqpaid_paid_bot';
$apiUrl = "https://api.telegram.org/bot{$botToken}/";
$webhookUrl = 'https://app.uniqpaid.com/test3/bot/webhook.php';
$webappUrl = 'https://app.uniqpaid.com/test3/';

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Полное обновление бота @uniqpaid_paid_bot</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .bot-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #00ff88;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(0, 255, 136, 0.2); 
            border-color: #00ff88; 
            color: #00ff88; 
        }
        .error { 
            background: rgba(255, 68, 68, 0.2); 
            border-color: #ff4444; 
            color: #ff6666; 
        }
        .info { 
            background: rgba(0, 191, 255, 0.2); 
            border-color: #00bfff; 
            color: #87ceeb; 
        }
        .warning { 
            background: rgba(255, 193, 7, 0.2); 
            border-color: #ffc107; 
            color: #ffd700; 
        }
        .step {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            margin: 20px 0;
            border-radius: 15px;
            border-left: 5px solid #00bfff;
        }
        .code {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
            white-space: pre-wrap;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        button {
            background: linear-gradient(135deg, #00ff88, #00bfff);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        button:hover {
            background: linear-gradient(135deg, #00bfff, #00ff88);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .progress {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 2px;
            margin: 10px 0;
        }
        .progress-bar {
            background: linear-gradient(135deg, #00ff88, #00bfff);
            height: 20px;
            border-radius: 8px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Обновление бота @<?php echo $botUsername; ?></h1>
        
        <div class="bot-info">
            <h3>📋 Информация о боте:</h3>
            <div class="code">Бот: @<?php echo $botUsername; ?>
Токен: <?php echo substr($botToken, 0, 15); ?>...
Webhook: <?php echo $webhookUrl; ?>
WebApp: <?php echo $webappUrl; ?>
API URL: <?php echo $apiUrl; ?></div>
        </div>

        <?php
        /**
         * Функция для запроса к Telegram API
         */
        function telegramApiCall($method, $data = []) {
            global $apiUrl;
            
            $url = $apiUrl . $method;
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_USERAGENT, 'UniQPaid Bot Updater/1.0');
            
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($result === false || $error) {
                return ['error' => "cURL ошибка: {$error}"];
            }
            
            if ($httpCode !== 200) {
                return ['error' => "HTTP ошибка: {$httpCode}"];
            }
            
            $response = json_decode($result, true);
            
            if (!$response || !isset($response['ok'])) {
                return ['error' => 'Неверный ответ API'];
            }
            
            if (!$response['ok']) {
                $errorMsg = $response['description'] ?? 'Неизвестная ошибка';
                return ['error' => "API ошибка: {$errorMsg}"];
            }
            
            return ['success' => true, 'data' => $response['result']];
        }

        // Если запрос на обновление
        if (isset($_GET['action']) && $_GET['action'] === 'update') {
            echo '<div class="step">';
            echo '<h3>🚀 Выполняем полное обновление бота...</h3>';
            
            $totalSteps = 7;
            $currentStep = 0;
            
            // Функция для отображения прогресса
            function showProgress($step, $total, $message) {
                $percent = ($step / $total) * 100;
                echo '<div class="status info">' . $step . '/' . $total . '. ' . $message . '</div>';
                echo '<div class="progress"><div class="progress-bar" style="width: ' . $percent . '%"></div></div>';
                flush();
                ob_flush();
            }
            
            // 1. Проверка бота
            showProgress(++$currentStep, $totalSteps, 'Проверка доступности бота...');
            $botInfo = telegramApiCall('getMe');
            if (isset($botInfo['error'])) {
                echo '<div class="status error">❌ ' . $botInfo['error'] . '</div>';
                echo '</div></body></html>';
                exit;
            } else {
                $bot = $botInfo['data'];
                echo '<div class="status success">✅ Бот найден: @' . $bot['username'] . ' (ID: ' . $bot['id'] . ')</div>';
            }
            
            // 2. Удаление старого webhook
            showProgress(++$currentStep, $totalSteps, 'Удаление старого webhook...');
            $deleteResult = telegramApiCall('deleteWebhook', ['drop_pending_updates' => true]);
            if (isset($deleteResult['error'])) {
                echo '<div class="status warning">⚠️ ' . $deleteResult['error'] . '</div>';
            } else {
                echo '<div class="status success">✅ Старый webhook удален, кэш сброшен</div>';
            }
            
            sleep(2);
            
            // 3. Установка нового webhook
            showProgress(++$currentStep, $totalSteps, 'Установка нового webhook...');
            $setWebhookResult = telegramApiCall('setWebhook', [
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query', 'inline_query'],
                'drop_pending_updates' => true,
                'secret_token' => 'UniQPaid_' . substr(md5($botToken), 0, 16)
            ]);
            if (isset($setWebhookResult['error'])) {
                echo '<div class="status error">❌ ' . $setWebhookResult['error'] . '</div>';
            } else {
                echo '<div class="status success">✅ Новый webhook установлен</div>';
            }
            
            // 4. Настройка команд бота
            showProgress(++$currentStep, $totalSteps, 'Обновление команд бота...');
            $commands = [
                [
                    'command' => 'start',
                    'description' => '🚀 Запустить бота и открыть мини-приложение'
                ],
                [
                    'command' => 'help',
                    'description' => '❓ Помощь по использованию бота'
                ],
                [
                    'command' => 'balance',
                    'description' => '💰 Проверить баланс монет'
                ],
                [
                    'command' => 'referral',
                    'description' => '👥 Реферальная программа'
                ]
            ];
            
            $commandsResult = telegramApiCall('setMyCommands', ['commands' => $commands]);
            if (isset($commandsResult['error'])) {
                echo '<div class="status warning">⚠️ ' . $commandsResult['error'] . '</div>';
            } else {
                echo '<div class="status success">✅ Команды бота обновлены (' . count($commands) . ' команд)</div>';
            }
            
            // 5. Настройка описания бота
            showProgress(++$currentStep, $totalSteps, 'Обновление описания бота...');
            $description = "🎯 UniQPaid - получайте криптовалюту за просмотр рекламы!\n\n💰 10 монет за каждый просмотр\n🔄 Вывод в BTC, USDT, TON, ETH\n👥 Реферальная программа 10%\n\n🚀 Нажмите /start чтобы начать!";
            
            $descResult = telegramApiCall('setMyDescription', ['description' => $description]);
            if (isset($descResult['error'])) {
                echo '<div class="status warning">⚠️ ' . $descResult['error'] . '</div>';
            } else {
                echo '<div class="status success">✅ Описание бота обновлено</div>';
            }
            
            // 6. Настройка короткого описания
            showProgress(++$currentStep, $totalSteps, 'Обновление короткого описания...');
            $shortDesc = "💰 Получайте криптовалюту за просмотр рекламы! 10 монет за просмотр, вывод в BTC/USDT/TON/ETH";
            
            $shortDescResult = telegramApiCall('setMyShortDescription', ['short_description' => $shortDesc]);
            if (isset($shortDescResult['error'])) {
                echo '<div class="status warning">⚠️ ' . $shortDescResult['error'] . '</div>';
            } else {
                echo '<div class="status success">✅ Короткое описание обновлено</div>';
            }
            
            // 7. Финальная проверка
            showProgress(++$currentStep, $totalSteps, 'Финальная проверка настроек...');
            $finalCheck = telegramApiCall('getWebhookInfo');
            if (isset($finalCheck['error'])) {
                echo '<div class="status error">❌ ' . $finalCheck['error'] . '</div>';
            } else {
                $webhookInfo = $finalCheck['data'];
                if ($webhookInfo['url']) {
                    echo '<div class="status success">✅ Webhook активен: ' . $webhookInfo['url'] . '</div>';
                    if (isset($webhookInfo['pending_update_count'])) {
                        echo '<div class="status info">📊 Ожидающих обновлений: ' . $webhookInfo['pending_update_count'] . '</div>';
                    }
                } else {
                    echo '<div class="status error">❌ Webhook не установлен</div>';
                }
            }
            
            echo '<div class="status success">🎉 ПОЛНОЕ ОБНОВЛЕНИЕ БОТА ЗАВЕРШЕНО!</div>';
            echo '<div class="status info">📱 Теперь можно тестировать: @' . $botUsername . '</div>';
            echo '</div>';
            
        } else {
            // Показываем кнопку для запуска
            echo '<div class="step">';
            echo '<h3>🚀 Готов к полному обновлению бота?</h3>';
            echo '<p><strong>Это действие выполнит:</strong></p>';
            echo '<ul>';
            echo '<li>✅ Проверку доступности бота</li>';
            echo '<li>✅ Удаление старого webhook</li>';
            echo '<li>✅ Установку нового webhook</li>';
            echo '<li>✅ Обновление команд бота</li>';
            echo '<li>✅ Настройку описания бота</li>';
            echo '<li>✅ Сброс кэша Telegram</li>';
            echo '<li>✅ Финальную проверку</li>';
            echo '</ul>';
            echo '<button onclick="window.location.href=\'?action=update\'">🚀 ОБНОВИТЬ БОТА</button>';
            echo '</div>';
        }
        ?>

        <div class="step">
            <h3>📋 После обновления:</h3>
            <ol>
                <li>Откройте @<?php echo $botUsername; ?> в Telegram</li>
                <li>Отправьте команду <code>/start</code></li>
                <li>Нажмите кнопку "Запустить приложение"</li>
                <li>Проверьте что мини-приложение загружается</li>
                <li>Убедитесь что отображается баланс и история</li>
            </ol>
        </div>

        <div class="status warning">
            ⚠️ <strong>Важные моменты:</strong>
            <ul>
                <li>Убедитесь что .htaccess файлы обновлены на сервере</li>
                <li>X-Frame-Options должен быть установлен в ALLOWALL</li>
                <li>После обновления может потребоваться 2-3 минуты для применения изменений</li>
                <li>Если проблемы остаются - перезапустите Telegram</li>
            </ul>
        </div>
    </div>

    <script>
        // Автообновление страницы каждые 30 секунд во время обновления
        if (window.location.search.includes('action=update')) {
            setTimeout(() => {
                if (!document.querySelector('.status.success:last-child')) {
                    location.reload();
                }
            }, 30000);
        }
    </script>
</body>
</html>
