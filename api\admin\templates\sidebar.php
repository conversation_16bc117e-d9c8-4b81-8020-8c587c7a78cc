<?php
/**
 * api/admin/templates/sidebar.php
 * Единое боковое меню для всех страниц админки
 */

// Определяем текущую страницу
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>

<!-- Боковое меню -->
<nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>" href="index.php">
                    <i class="bi bi-speedometer2 me-2"></i>
                    Панель управления
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>" href="users.php">
                    <i class="bi bi-people me-2"></i>
                    Пользователи
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'stats' ? 'active' : ''; ?>" href="stats.php">
                    <i class="bi bi-bar-chart me-2"></i>
                    Статистика
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'withdrawals' ? 'active' : ''; ?>" href="withdrawals.php">
                    <i class="bi bi-cash-stack me-2"></i>
                    Отчёты по выводам
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'balance' ? 'active' : ''; ?>" href="balance.php">
                    <i class="bi bi-wallet2 me-2"></i>
                    Баланс NOWPayments
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'calculator' ? 'active' : ''; ?>" href="calculator.php">
                    <i class="bi bi-calculator me-2"></i>
                    Калькулятор NOWPayments
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'cache_management' ? 'active' : ''; ?>" href="cache_management.php">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    Управление кэшем
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="bi bi-gear me-2"></i>
                    Настройки
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'security' ? 'active' : ''; ?>" href="security.php">
                    <i class="bi bi-shield-lock me-2"></i>
                    Безопасность
                </a>
            </li>
            
            <!-- Дополнительные разделы (если есть) -->
            <?php if (file_exists('monitor.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'monitor' ? 'active' : ''; ?>" href="monitor.php">
                    <i class="bi bi-activity me-2"></i>
                    Мониторинг
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (file_exists('debug_withdrawals.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'debug_withdrawals' ? 'active' : ''; ?>" href="debug_withdrawals.php">
                    <i class="bi bi-bug me-2"></i>
                    Отладка выводов
                </a>
            </li>
            <?php endif; ?>
            
            <!-- Разделитель -->
            <hr class="my-3">
            
            <!-- Выход -->
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="bi bi-box-arrow-right me-2"></i>
                    Выход
                </a>
            </li>
        </ul>
        
        <!-- Информация о пользователе -->
        <div class="mt-4 p-3 bg-light rounded">
            <small class="text-muted">
                <i class="bi bi-person-circle me-1"></i>
                Администратор<br>
                <i class="bi bi-clock me-1"></i>
                <?php echo date('H:i, d.m.Y'); ?>
            </small>
        </div>
    </div>
</nav>
