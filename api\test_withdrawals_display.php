<?php
/**
 * Тестовый скрипт для проверки отображения выплат
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/functions.php';

header('Content-Type: text/plain; charset=utf-8');

echo "=== ТЕСТ ОТОБРАЖЕНИЯ ВЫПЛАТ ===\n\n";

// Загружаем данные пользователей
$userData = loadUserData();
if (!$userData) {
    echo "❌ Не удалось загрузить данные пользователей\n";
    exit;
}

echo "✅ Данные пользователей загружены\n";
echo "Всего пользователей: " . count($userData) . "\n\n";

// Собираем все выплаты
$allWithdrawals = [];
$currencies = [];
$statuses = [];

foreach ($userData as $userId => $user) {
    if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
        echo "Пользователь {$userId}:\n";
        echo "  Имя: " . ($user['first_name'] ?? 'N/A') . " " . ($user['last_name'] ?? '') . "\n";
        echo "  Username: " . ($user['username'] ?? 'N/A') . "\n";
        echo "  Выплат: " . count($user['withdrawals']) . "\n";
        
        foreach ($user['withdrawals'] as $index => $withdrawal) {
            echo "  Выплата #{$index}:\n";
            echo "    ID: " . ($withdrawal['id'] ?? 'N/A') . "\n";
            echo "    Payout ID: " . ($withdrawal['payout_id'] ?? 'N/A') . "\n";
            echo "    Статус: " . ($withdrawal['status'] ?? 'N/A') . "\n";
            echo "    Сумма: " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет\n";
            echo "    Валюта: " . ($withdrawal['currency'] ?? 'N/A') . "\n";
            echo "    Адрес: " . ($withdrawal['wallet_address'] ?? $withdrawal['address'] ?? 'N/A') . "\n";
            echo "    Дата: " . (isset($withdrawal['timestamp']) ? date('Y-m-d H:i:s', $withdrawal['timestamp']) : 'N/A') . "\n";
            echo "\n";
            
            // Добавляем информацию о пользователе
            $withdrawal['user_id'] = $userId;
            $withdrawal['user_name'] = $user['first_name'] ?? 'Неизвестно';
            $withdrawal['user_lastname'] = $user['last_name'] ?? '';
            $withdrawal['username'] = $user['username'] ?? '';
            $withdrawal['withdrawal_index'] = $index;
            
            // Собираем уникальные валюты и статусы
            if (isset($withdrawal['currency'])) {
                $currencies[$withdrawal['currency']] = true;
            }
            if (isset($withdrawal['status'])) {
                $statuses[$withdrawal['status']] = true;
            }
            
            $allWithdrawals[] = $withdrawal;
        }
        echo "\n";
    }
}

echo "=== ИТОГОВАЯ СТАТИСТИКА ===\n";
echo "Всего выплат: " . count($allWithdrawals) . "\n";
echo "Уникальных валют: " . count($currencies) . " (" . implode(', ', array_keys($currencies)) . ")\n";
echo "Уникальных статусов: " . count($statuses) . " (" . implode(', ', array_keys($statuses)) . ")\n";

// Сортируем по времени (новые сначала)
usort($allWithdrawals, function($a, $b) {
    return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
});

echo "\n=== СПИСОК ВСЕХ ВЫПЛАТ (ОТСОРТИРОВАННЫЙ) ===\n";
foreach ($allWithdrawals as $i => $withdrawal) {
    echo ($i + 1) . ". ";
    echo "Пользователь: {$withdrawal['user_name']} ({$withdrawal['user_id']}) | ";
    echo "Payout ID: " . ($withdrawal['payout_id'] ?? $withdrawal['id'] ?? 'N/A') . " | ";
    echo "Статус: {$withdrawal['status']} | ";
    echo "Сумма: {$withdrawal['coins_amount']} монет | ";
    echo "Валюта: {$withdrawal['currency']} | ";
    echo "Дата: " . (isset($withdrawal['timestamp']) ? date('Y-m-d H:i:s', $withdrawal['timestamp']) : 'N/A') . "\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
