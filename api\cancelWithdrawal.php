<?php
/**
 * cancelWithdrawal.php
 * Отмена выплаты пользователем
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_mock.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

error_log("cancelWithdrawal INFO: Получен запрос на отмену выплаты");

try {
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['initData']) || !isset($input['withdrawal_id'])) {
        throw new Exception('Отсутствуют обязательные параметры');
    }
    
    $initData = $input['initData'];
    $withdrawalId = $input['withdrawal_id'];
    
    error_log("cancelWithdrawal INFO: Отмена выплаты ID: {$withdrawalId}");
    
    // Валидируем initData
    $userData = validateInitData($initData);
    if (!$userData) {
        throw new Exception('Неверные данные авторизации');
    }
    
    $userId = $userData['id'];
    error_log("cancelWithdrawal INFO: initData валидирован для user {$userId}");
    
    // Загружаем данные пользователя
    $allUserData = loadUserData();
    if (!isset($allUserData[$userId])) {
        throw new Exception('Пользователь не найден');
    }
    
    $userInfo = $allUserData[$userId];
    
    // Ищем выплату в истории пользователя
    $withdrawalToCancel = null;
    $withdrawalIndex = -1;
    
    if (isset($userInfo['withdrawals']) && is_array($userInfo['withdrawals'])) {
        foreach ($userInfo['withdrawals'] as $index => $withdrawal) {
            if (isset($withdrawal['id']) && $withdrawal['id'] === $withdrawalId) {
                $withdrawalToCancel = $withdrawal;
                $withdrawalIndex = $index;
                break;
            }
        }
    }
    
    if (!$withdrawalToCancel) {
        throw new Exception('Выплата не найдена');
    }
    
    // Проверяем, можно ли отменить выплату
    $cancellableStatuses = ['pending', 'processing', 'В ОБРАБОТКЕ'];
    if (!in_array($withdrawalToCancel['status'], $cancellableStatuses)) {
        throw new Exception('Эту выплату нельзя отменить (статус: ' . $withdrawalToCancel['status'] . ')');
    }
    
    error_log("cancelWithdrawal INFO: Выплата найдена и может быть отменена");
    
    // Пытаемся отменить выплату через NOWPayments API (если есть ID)
    $apiCancelled = false;
    if (isset($withdrawalToCancel['id']) && !empty($withdrawalToCancel['id'])) {
        try {
            $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
            
            // Пытаемся получить статус выплаты
            $payoutStatus = $api->getPayoutStatus($withdrawalToCancel['id']);
            
            if ($payoutStatus && isset($payoutStatus['status'])) {
                $currentStatus = $payoutStatus['status'];
                error_log("cancelWithdrawal INFO: Текущий статус в NOWPayments: {$currentStatus}");
                
                // Если выплата еще не обработана, можно попытаться отменить
                if (in_array($currentStatus, ['pending', 'processing', 'waiting'])) {
                    // NOWPayments не имеет прямого API для отмены, но мы можем пометить как отмененную
                    error_log("cancelWithdrawal INFO: Выплата в NOWPayments еще обрабатывается");
                } else {
                    error_log("cancelWithdrawal WARNING: Выплата в NOWPayments уже обработана: {$currentStatus}");
                }
            }
        } catch (Exception $e) {
            error_log("cancelWithdrawal WARNING: Не удалось проверить статус в NOWPayments: " . $e->getMessage());
        }
    }
    
    // Возвращаем средства на баланс пользователя
    $returnedAmount = $withdrawalToCancel['coins_amount'];
    $allUserData[$userId]['balance'] += $returnedAmount;
    
    // Обновляем статус выплаты на "отменена"
    $allUserData[$userId]['withdrawals'][$withdrawalIndex]['status'] = 'cancelled';
    $allUserData[$userId]['withdrawals'][$withdrawalIndex]['cancelled_at'] = date('Y-m-d H:i:s');
    $allUserData[$userId]['withdrawals'][$withdrawalIndex]['cancelled_by_user'] = true;
    
    // Добавляем запись в лог
    if (!isset($allUserData[$userId]['withdrawal_log'])) {
        $allUserData[$userId]['withdrawal_log'] = [];
    }
    
    $allUserData[$userId]['withdrawal_log'][] = [
        'action' => 'cancelled',
        'withdrawal_id' => $withdrawalId,
        'returned_amount' => $returnedAmount,
        'timestamp' => time(),
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Сохраняем данные
    if (!saveUserData($allUserData)) {
        throw new Exception('Ошибка сохранения данных');
    }
    
    error_log("cancelWithdrawal SUCCESS: Выплата {$withdrawalId} отменена, возвращено {$returnedAmount} монет");
    
    // Возвращаем успешный ответ
    echo json_encode([
        'success' => true,
        'message' => 'Выплата отменена',
        'returned_amount' => $returnedAmount,
        'new_balance' => $allUserData[$userId]['balance'],
        'withdrawal_id' => $withdrawalId
    ]);
    
} catch (Exception $e) {
    error_log("cancelWithdrawal ERROR: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage()
    ]);
}
?>
