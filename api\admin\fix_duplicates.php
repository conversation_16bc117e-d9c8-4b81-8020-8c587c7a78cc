<?php
/**
 * Скрипт для исправления дублирующихся записей выплат
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../functions.php';

// Проверяем авторизацию администратора
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Требуется авторизация администратора']);
    exit;
}

header('Content-Type: application/json');

try {
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!$userData) {
        throw new Exception("Не удалось загрузить данные пользователей");
    }
    
    $totalDuplicates = 0;
    $totalFixed = 0;
    $report = [];
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }
        
        $originalCount = count($user['withdrawals']);
        $seenPayoutIds = [];
        $uniqueWithdrawals = [];
        $userDuplicates = 0;
        
        foreach ($user['withdrawals'] as $index => $withdrawal) {
            // Создаем уникальный ключ
            $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
            
            if ($payoutId) {
                if (isset($seenPayoutIds[$payoutId])) {
                    // Найден дубликат
                    $userDuplicates++;
                    $totalDuplicates++;
                    continue;
                }
                $seenPayoutIds[$payoutId] = true;
            }
            
            $uniqueWithdrawals[] = $withdrawal;
        }
        
        // Если были дубликаты, обновляем массив
        if ($userDuplicates > 0) {
            $user['withdrawals'] = $uniqueWithdrawals;
            $user['withdrawals_count'] = count($uniqueWithdrawals);
            $totalFixed++;
            
            $report[] = [
                'user_id' => $userId,
                'original_count' => $originalCount,
                'unique_count' => count($uniqueWithdrawals),
                'duplicates_removed' => $userDuplicates
            ];
        }
    }
    
    // Сохраняем данные если были изменения
    if ($totalFixed > 0) {
        if (saveUserData($userData)) {
            echo json_encode([
                'success' => true,
                'total_duplicates' => $totalDuplicates,
                'users_fixed' => $totalFixed,
                'report' => $report,
                'message' => "Исправлено дубликатов: {$totalDuplicates} у {$totalFixed} пользователей"
            ]);
        } else {
            throw new Exception("Не удалось сохранить исправленные данные");
        }
    } else {
        echo json_encode([
            'success' => true,
            'total_duplicates' => 0,
            'users_fixed' => 0,
            'report' => [],
            'message' => "Дубликаты не найдены"
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
