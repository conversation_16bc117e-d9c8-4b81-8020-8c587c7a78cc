Stack trace:
Frame         Function      Args
0007FFFFBDC0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBDC0, 0007FFFFACC0) msys-2.0.dll+0x2118E
0007FFFFBDC0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFBDC0  0002100469F2 (00021028DF99, 0007FFFFBC78, 0007FFFFBDC0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBDC0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBDC0  00021006A545 (0007FFFFBDD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBDD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9735B0000 ntdll.dll
7FF9732F0000 KERNEL32.DLL
7FF971020000 KERNELBASE.dll
7FF9718F0000 USER32.dll
7FF970E10000 win32u.dll
7FF971A90000 GDI32.dll
7FF970CD0000 gdi32full.dll
7FF970ED0000 msvcp_win.dll
7FF9712F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF971770000 advapi32.dll
7FF971610000 msvcrt.dll
7FF972B60000 sechost.dll
7FF972F60000 RPCRT4.dll
7FF970560000 CRYPTBASE.DLL
7FF970E40000 bcryptPrimitives.dll
7FF972B30000 IMM32.DLL
