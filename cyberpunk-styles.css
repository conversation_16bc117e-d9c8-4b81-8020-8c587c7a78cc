/* ======================================== */
/* CRYPTO WALLET TELEGRAM MINI APP - ELEGANT STYLE */
/* ======================================== */

/* Import elegant fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap");

/* --- ELEGANT COLOR PALETTE & VARIABLES --- */
:root {
  /* Elegant Color Scheme - Inspired by Crypto Wallet Design */
  --primary-bg: linear-gradient(135deg, #FFF8E7 0%, #FFE4B5 50%, #FFD700 100%);
  --secondary-bg: #FFFFFF;
  --card-bg: #FFFFFF;
  --accent-primary: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  --accent-secondary: linear-gradient(135deg, #FF8C00 0%, #FF6347 100%);
  --text-primary: #2C2C2C;
  --text-secondary: #666666;
  --text-muted: #999999;
  --border-light: #E5E5E5;
  --border-medium: #CCCCCC;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --shadow-strong: rgba(0, 0, 0, 0.25);
  --success-color: #4CAF50;
  --error-color: #F44336;
  --warning-color: #FF9800;

  /* Legacy compatibility */
  --app-bg-color: var(--secondary-bg);
  --app-secondary-bg-color: var(--card-bg);
  --app-text-color: var(--text-primary);
  --app-hint-color: var(--text-secondary);
  --app-primary-color: var(--accent-primary);
  --app-primary-text-color: var(--text-primary);
  --app-secondary-button-bg: var(--accent-secondary);
  --app-secondary-button-text: var(--secondary-bg);
  --app-destructive-color: var(--error-color);
  --app-separator-color: var(--border-light);

  /* Button colors - unified elegant style */
  --button-primary: var(--accent-primary);
  --button-secondary: var(--accent-secondary);
  --page-transition-duration: 0.3s;

  /* Sprite settings */
  --sprite-url: "images/sprite.svg";
  --icon-width: 24px;
  --icon-height: 24px;
  --sprite-total-width: calc(var(--icon-width) * 3);
  --sprite-total-height: calc(var(--icon-height) * 3);
}

/* --- ELEGANT ANIMATIONS --- */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes gentlePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* --- ELEGANT BACKGROUND --- */
.cyberpunk-coins-bg {
  display: none; /* Remove cyberpunk background */
}

/* Remove glitch lines */
.glitch-line {
  display: none;
}

/* --- GLOBAL STYLES --- */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, sans-serif;
  color: var(--text-primary);
  background:
    linear-gradient(135deg, #FFF8E1 0%, #FFECB3 25%, #FFE082 50%, #FFECB3 75%, #FFF8E1 100%),
    radial-gradient(circle at 20% 80%, rgba(255, 193, 7, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 152, 0, 0.08) 0%, transparent 50%);
  background-attachment: fixed;
  overscroll-behavior: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  position: relative;
  font-weight: 400;
  line-height: 1.5;
}

/* Remove unnecessary pseudo-elements */
body::before,
body::after,
.app-container::after {
  display: none;
}

/* --- MAIN CONTAINER --- */
.app-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 0 16px 80px 16px;
  position: relative;
  overflow-x: hidden;
  z-index: 1;
  max-width: 480px;
  margin: 0 auto;
}

.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    transparent 0%,
    #FF8C00 20%,
    #FFA500 40%,
    #FFD700 60%,
    #FFA500 80%,
    transparent 100%);
  z-index: 1000;
  animation: glitchStripe 3s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

@keyframes glitchStripe {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100vw);
  }
}

/* --- HEADER STYLES --- */
.app-header {
  width: 100%;
  background: var(--secondary-bg);
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-light);
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  max-width: 480px;
  z-index: 10;
  box-shadow: 0 2px 8px var(--shadow-light);
  height: 64px;
}

.app-header::before {
  display: none;
}

.user-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  overflow: hidden;
  border: 2px solid #FFA500;
  background: linear-gradient(135deg, #FF8C00, #FFA500) !important;
  box-shadow: 0 0 12px rgba(255, 165, 0, 0.4);
  position: relative;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100vw - 200px);
}

.balance-info {
  display: flex;
  align-items: center;
  background: var(--accent-primary);
  padding: 8px 16px;
  border-radius: 20px;
  flex-shrink: 0;
  transition: all 0.3s ease;
  min-width: 90px;
  height: 36px;
  justify-content: center;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.balance-info:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

.balance-amount {
  font-size: 14px;
  font-weight: 600;
  margin-right: 4px;
  color: var(--text-primary);
  white-space: nowrap;
}

.balance-currency {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 500;
  white-space: nowrap;
  opacity: 0.8;
}

/* --- SECTIONS & PAGES --- */
.app-main,
.app-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  position: fixed;
  top: 64px;
  left: 50%;
  transform: translateX(-50%);
  max-width: 480px;
  bottom: 80px;
  padding: 20px 16px;
  opacity: 1;
  transition: opacity var(--page-transition-duration) ease-in-out;
  will-change: opacity;
  background-color: transparent;
  z-index: 1;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

.app-main.active-section,
.app-section.active-section {
  z-index: 2;
}

.page-hidden {
  display: none !important;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
  z-index: 2;
  display: flex !important;
  visibility: visible;
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  z-index: 2;
  display: flex !important;
  visibility: visible;
}

.page-leave-active {
  opacity: 0;
  transform: translateX(-20px);
  z-index: 1;
  pointer-events: none;
  display: flex !important;
}

.app-main h2,
.app-section h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 24px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 28px;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.5px;
  position: relative;
}

.app-main h2::before,
.app-section h2::before {
  content: "";
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23FF8F00'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  vertical-align: middle;
  filter: drop-shadow(0 0 4px rgba(255, 143, 0, 0.3));
}

.app-main h2::after,
.app-section h2::after {
  display: none;
}

/* --- STATUS MESSAGE --- */
.status-message {
  padding: 16px 20px;
  font-size: 14px;
  text-align: center;
  min-height: 20px;
  border-radius: 12px;
  word-wrap: break-word;
  margin-bottom: 20px;
  background: var(--card-bg);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  opacity: 1;
  font-weight: 500;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.status-message:empty {
  opacity: 0;
  padding: 0;
  min-height: 0;
  margin-bottom: 0;
}

.status-message.success {
  color: var(--success-color);
  background: rgba(76, 175, 80, 0.1);
  border-color: var(--success-color);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.status-message.error {
  color: var(--error-color);
  background: rgba(244, 67, 54, 0.1);
  border-color: var(--error-color);
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2);
}

/* --- ELEGANT BUTTONS --- */
.action-button {
  width: 100%;
  padding: 18px 28px;
  font-size: 16px;
  font-weight: 700;
  border: none;
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  appearance: none;
  -webkit-appearance: none;
  text-align: center;
  font-family: "Inter", sans-serif;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(145deg, #FFD54F, #FFC107);
  color: #2C2C2C;
  box-shadow:
    0 12px 24px rgba(255, 193, 7, 0.4),
    0 6px 12px rgba(255, 193, 7, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(255, 152, 0, 0.3);
  transform: translateY(0);
}

.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow:
    0 16px 32px rgba(255, 193, 7, 0.5),
    0 8px 16px rgba(255, 193, 7, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.5),
    inset 0 -2px 0 rgba(255, 152, 0, 0.4);
  background: linear-gradient(145deg, #FFEB3B, #FFD54F);
}

.action-button:active {
  transform: translateY(1px);
  box-shadow:
    0 6px 12px rgba(255, 193, 7, 0.3),
    0 3px 6px rgba(255, 193, 7, 0.2),
    inset 0 1px 0 rgba(0, 0, 0, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(145deg, #FFC107, #FF9800);
}

/* Button variants - all use same elegant style */
.action-button.purple-button,
.action-button.orange-button,
.action-button.yellow-button {
  background: linear-gradient(145deg, #FFD54F, #FFC107);
  box-shadow:
    0 12px 24px rgba(255, 193, 7, 0.4),
    0 6px 12px rgba(255, 193, 7, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(255, 152, 0, 0.3);
}

.action-button.purple-button:hover,
.action-button.orange-button:hover,
.action-button.yellow-button:hover {
  transform: translateY(-3px);
  box-shadow:
    0 16px 32px rgba(255, 193, 7, 0.5),
    0 8px 16px rgba(255, 193, 7, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.5),
    inset 0 -2px 0 rgba(255, 152, 0, 0.4);
  background: linear-gradient(145deg, #FFEB3B, #FFD54F);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: var(--border-medium) !important;
  color: var(--text-muted) !important;
  box-shadow: none !important;
  transform: none !important;
  pointer-events: none !important;
}

.action-button:disabled::before {
  display: none;
}

.action-button:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Pressed effect */
.action-button.pressed {
  transform: translateY(2px) scale(0.98);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4), inset 0 2px 5px rgba(0, 0, 0, 0.2);
  pointer-events: none !important;
  cursor: not-allowed !important;
  opacity: 0.7;
}

/* Countdown overlay */
.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.9),
    rgba(26, 26, 46, 0.9)
  );
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 900;
  color: #FFFFFF;
  border-radius: 12px;
  z-index: 10;
  font-family: "Orbitron", monospace;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 165, 0, 0.6),
    0 0 30px rgba(255, 165, 0, 0.4),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: pulse 1s ease-in-out infinite;
  pointer-events: all !important;
  cursor: not-allowed !important;
  border: 2px solid rgba(255, 165, 0, 0.3);
  box-shadow:
    inset 0 0 20px rgba(255, 165, 0, 0.1),
    0 0 20px rgba(0, 0, 0, 0.5);
}

/* Дополнительные стили для максимальной контрастности таймера */
.countdown-overlay::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 40px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  z-index: -1;
  border: 1px solid rgba(255, 165, 0, 0.5);
}

/* Стили для цифр таймера */
.countdown-overlay .countdown-time {
  position: relative;
  z-index: 2;
  background: linear-gradient(135deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 900;
  font-size: 26px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
}

/* --- NAVIGATION --- */
.app-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  max-width: 480px;
  width: calc(100% - 32px);
  background: var(--secondary-bg);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 16px max(12px, env(safe-area-inset-bottom)) 16px;
  border-radius: 24px 24px 0 0;
  border-top: 1px solid var(--border-light);
  z-index: 100;
  height: 80px;
  box-shadow: 0 -4px 20px var(--shadow-light);
  margin-bottom: 0;
}

.app-nav::before {
  display: none;
}

.nav-button {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  font-size: 12px;
  flex-grow: 1;
  transition: all 0.3s ease;
  border-radius: 12px;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.nav-button:hover {
  color: var(--text-secondary);
  background: rgba(255, 215, 0, 0.1);
}

.nav-button.active {
  color: #2C2C2C;
  background: linear-gradient(145deg, #FFD54F, #FFC107);
  border-radius: 18px;
  box-shadow:
    0 8px 16px rgba(255, 193, 7, 0.4),
    0 4px 8px rgba(255, 193, 7, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(255, 152, 0, 0.3);
  border: none;
  transform: translateY(-2px);
}

.nav-button .nav-icon,
.nav-button .cyber-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
  filter: brightness(0) invert(0.4);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-button.active .nav-icon {
  filter: brightness(0) saturate(100%) invert(69%) sepia(68%) saturate(497%)
    hue-rotate(83deg) brightness(99%) contrast(93%);
  animation: neonGlow 2s ease-in-out infinite;
}

/* Контрастные SVG иконки навигации */
.nav-button .cyber-icon {
  fill: #666666;
  stroke: #666666;
  stroke-width: 1.5;
  filter: none;
  transition: all 0.3s ease;
}

.nav-button:hover .cyber-icon {
  fill: #FFA500;
  stroke: #FFA500;
  filter: drop-shadow(0 0 4px rgba(255, 165, 0, 0.5));
}

.nav-button.active .cyber-icon {
  fill: #1A1A1A;
  stroke: #1A1A1A;
  filter: drop-shadow(0 0 8px rgba(26, 26, 26, 0.8));
}

.nav-button:active {
  transform: translateY(0) scale(0.95);
}

/* --- CONTENT BLOCKS --- */
.friends-block,
.earn-block {
  background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
  padding: 28px;
  border-radius: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 193, 7, 0.3);
  box-shadow:
    0 8px 20px rgba(255, 193, 7, 0.15),
    0 4px 10px rgba(255, 152, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(255, 193, 7, 0.1);
  position: relative;
  animation: fadeIn 0.6s ease-out;
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

.friends-block::before,
.earn-block::before {
  display: none;
}

.friends-block h3,
.earn-block h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.3px;
}

.friends-block p,
.earn-block p {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 0;
  margin-bottom: 16px;
  line-height: 1.6;
  font-family: "Inter", sans-serif;
}

.friends-block p.hint,
.earn-block p.hint {
  font-size: 13px;
  color: var(--text-muted);
  opacity: 0.8;
}

/* Принудительно показываем все элементы внутри блоков */
.friends-block *,
.earn-block * {
  max-height: none !important;
  overflow: visible !important;
}

/* Убираем любые ограничения высоты для всех дочерних элементов */
.friends-block > *,
.earn-block > * {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* --- FORMS --- */
.withdrawal-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.withdrawal-form label {
  font-size: 14px;
  color: var(--cyber-text-primary);
  margin-bottom: -10px;
  font-family: "Rajdhani", sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.withdrawal-form input[type="number"],
.withdrawal-form input[type="text"],
.withdrawal-form select {
  padding: 16px 20px;
  border: 1px solid var(--border-light);
  background: var(--card-bg);
  color: var(--text-primary);
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
  font-family: "Inter", sans-serif;
  transition: all 0.3s ease;
  box-shadow: var(--glow-subtle);
}

.withdrawal-form input:focus,
.withdrawal-form select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: var(--glow-orange);
}

.withdrawal-form select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2300FFFF%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 15px top 50%;
  background-size: 12px auto;
  padding-right: 40px;
}

/* Стили для option элементов */
.withdrawal-form select option {
  background: var(--cyber-bg-secondary);
  color: var(--cyber-text-primary);
  padding: 12px 15px;
  border: none;
  font-family: "Rajdhani", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

.withdrawal-form select option:hover,
.withdrawal-form select option:focus {
  background: var(--cyber-bg-tertiary);
  color: var(--cyber-accent-neon);
}

.withdrawal-form select option:checked {
  background: linear-gradient(
    135deg,
    var(--cyber-accent-neon),
    var(--cyber-accent-purple)
  );
  color: var(--cyber-bg-primary);
  font-weight: 600;
}

/* --- REFERRAL LINK AREA --- */
.referral-link-area {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
}

.referral-link-area input[type="text"] {
  flex-grow: 1;
  padding: 12px 16px;
  border: 1px solid var(--border-light);
  background: var(--card-bg);
  color: var(--text-primary);
  border-radius: 12px;
  font-size: 14px;
  font-family: "Inter", monospace;
  transition: all 0.3s ease;
  box-shadow: var(--glow-subtle);
}

.referral-link-area .copy-button {
  padding: 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border: 1px solid var(--accent-primary);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  box-shadow: var(--glow-orange);
}

.referral-link-area .copy-button:hover {
  transform: translateY(-2px);
  box-shadow:
    0 0 30px var(--shadow-orange),
    0 0 15px var(--shadow-neon);
  background: linear-gradient(135deg, var(--accent-secondary), var(--accent-primary));
  border-color: var(--accent-neon);
}

/* --- CALCULATOR STYLES --- */
.calculator-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--card-bg);
  border-radius: 16px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
}

.calculator-subtitle {
  margin: 0;
  color: var(--cyber-text-secondary);
  font-size: 14px;
  font-family: "Rajdhani", sans-serif;
}

.balance-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.balance-label {
  color: var(--cyber-text-secondary);
  font-size: 14px;
  font-family: "Rajdhani", sans-serif;
}

.balance-amount {
  color: var(--cyber-accent-neon);
  font-weight: 600;
  font-size: 16px;
  font-family: "Orbitron", monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.amount-input-section {
  background: linear-gradient(
    135deg,
    var(--cyber-bg-secondary),
    var(--cyber-bg-tertiary)
  );
  border: 2px solid var(--cyber-border);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  backdrop-filter: blur(10px);
}

.amount-input-section label {
  display: block;
  margin-bottom: 15px;
  font-weight: 600;
  color: var(--cyber-text-primary);
  font-size: 16px;
  font-family: "Orbitron", monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.input-group {
  position: relative;
  margin-bottom: 15px;
}

.input-group input {
  width: 100%;
  padding: 18px 90px 18px 18px;
  border: 1px solid var(--border-light);
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  box-sizing: border-box;
  background: var(--card-bg);
  color: var(--text-primary);
  transition: all 0.3s ease;
  font-family: "Inter", sans-serif;
  box-shadow: var(--glow-subtle);
}

.input-group input:focus {
  border-color: var(--accent-primary);
  box-shadow: var(--glow-orange);
  outline: none;
}

.input-suffix {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--cyber-text-secondary);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  font-family: "Rajdhani", sans-serif;
}

.amount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

#dollar-equivalent {
  font-weight: 600;
  color: var(--cyber-accent-neon);
  font-size: 18px;
  font-family: "Orbitron", monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.balance-status {
  padding: 6px 15px;
  border-radius: 25px;
  font-size: 12px;
  font-weight: 500;
  font-family: "Rajdhani", sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance-status.sufficient {
  background: rgba(57, 255, 20, 0.2);
  color: var(--cyber-success);
  border: 1px solid var(--cyber-success);
  box-shadow: 0 0 10px rgba(57, 255, 20, 0.3);
}

.balance-status.insufficient {
  background: rgba(255, 7, 58, 0.2);
  color: var(--cyber-error);
  border: 1px solid var(--cyber-error);
  box-shadow: 0 0 10px rgba(255, 7, 58, 0.3);
}

.balance-status.neutral {
  background: rgba(176, 176, 176, 0.2);
  color: var(--cyber-text-muted);
  border: 1px solid var(--cyber-text-muted);
}

/* --- CURRENCY TABS --- */
.currency-tabs-container {
  margin-top: 25px;
}

.currency-tabs-header {
  display: flex;
  background: var(--card-bg);
  border-radius: 16px;
  padding: 8px;
  margin-bottom: 24px;
  overflow-x: auto;
  gap: 6px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
  flex-wrap: nowrap;
  -webkit-overflow-scrolling: touch;
}

.currency-tab {
  flex: 1;
  min-width: 85px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 18px 14px;
  border: 1px solid rgba(255, 165, 0, 0.2);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  flex-shrink: 1;
  color: var(--text-secondary);
  border-radius: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  position: relative;
  font-family: "Inter", sans-serif;
  font-weight: 500;
  box-shadow:
    0 4px 8px rgba(255, 165, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.currency-tab:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  color: var(--text-primary);
  transform: translateY(-3px);
  box-shadow:
    0 6px 12px rgba(255, 165, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 165, 0, 0.4);
}

.currency-tab.active {
  background: linear-gradient(145deg, #FFB347, #FF8C00);
  color: #000000;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow:
    0 8px 16px rgba(255, 140, 0, 0.3),
    0 4px 8px rgba(255, 140, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  font-weight: 700;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}

/* Элегантные иконки валют в цветовой палитре приложения */
.currency-tab[data-currency="eth"] .tab-icon {
  color: #FFD700; /* Золотой */
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
}

.currency-tab[data-currency="btc"] .tab-icon {
  color: #FFA500; /* Оранжевый */
  filter: drop-shadow(0 0 4px rgba(255, 165, 0, 0.3));
}

.currency-tab[data-currency="usdttrc20"] .tab-icon {
  color: #FF8C00; /* Темно-оранжевый */
  filter: drop-shadow(0 0 4px rgba(255, 140, 0, 0.3));
}

.currency-tab[data-currency="ton"] .tab-icon {
  color: #FF9800; /* Оранжевый TON */
  filter: drop-shadow(0 0 4px rgba(255, 152, 0, 0.3));
}

/* Активные состояния с усиленным свечением в палитре приложения */
.currency-tab.active[data-currency="eth"] .tab-icon {
  color: #FFD700;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6)) brightness(1.2);
}

.currency-tab.active[data-currency="btc"] .tab-icon {
  color: #FFFFFF;
  filter: drop-shadow(0 0 8px rgba(255, 165, 0, 0.8)) drop-shadow(0 0 4px rgba(255, 255, 255, 0.6)) brightness(1.3);
}

.currency-tab.active[data-currency="usdttrc20"] .tab-icon {
  color: #FF8C00;
  filter: drop-shadow(0 0 8px rgba(255, 140, 0, 0.6)) brightness(1.2);
}

.currency-tab.active[data-currency="ton"] .tab-icon {
  color: #1A1A1A;
  filter: drop-shadow(0 0 8px rgba(26, 26, 26, 0.8)) brightness(1.2);
}

.tab-name {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.tab-symbol {
  font-size: 10px;
  opacity: 0.7;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  font-family: "Inter", monospace;
}

/* --- CURRENCY INFO CARD --- */
.currency-info-card {
  background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 24px;
  padding: 28px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 12px 24px rgba(255, 193, 7, 0.2),
    0 6px 12px rgba(255, 152, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(255, 193, 7, 0.1);
}

.currency-info-card::before {
  display: none;
}

.currency-title {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.currency-icon {
  font-size: 32px;
  color: #FF8F00;
  filter: drop-shadow(0 0 6px rgba(255, 143, 0, 0.4));
  display: inline-block;
  margin-right: 12px;
}

.currency-full-name {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: "Inter", sans-serif;
}

.currency-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  font-family: "Inter", sans-serif;
}

.currency-badge.status-best {
  background: var(--accent-primary);
  color: var(--text-primary);
  box-shadow: 0 2px 8px var(--shadow-light);
}

/* --- STATS & LISTS --- */
.referral-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  flex: 1;
  min-width: 120px;
  background: var(--card-bg);
  border-radius: 16px;
  padding: 16px;
  text-align: center;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
}

.stat-label {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 8px;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: "Inter", sans-serif;
}

.referrals-list,
.withdrawal-history {
  margin: 16px 0;
  background: var(--card-bg);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

.withdrawal-item {
  background: var(--card-bg);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px var(--shadow-light);
}

.withdrawal-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

.withdrawal-status {
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
  font-family: "Rajdhani", sans-serif;
  letter-spacing: 0.5px;
}

.status-pending {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.status-completed {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.status-failed {
  background: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.status-processing {
  background: linear-gradient(135deg, #FF9800, #FF6F00);
  color: white;
  box-shadow: 0 0 10px rgba(255, 152, 0, 0.3);
}

.status-confirmed {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  color: white;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.status-cancelled {
  background: linear-gradient(135deg, #888888, #666666);
  color: white;
  box-shadow: 0 0 10px rgba(136, 136, 136, 0.3);
}

.status-expired {
  background: linear-gradient(135deg, #ff8800, #cc6600);
  color: white;
  box-shadow: 0 0 10px rgba(255, 136, 0, 0.3);
}

.status-unknown {
  background: linear-gradient(135deg, #555555, #333333);
  color: #cccccc;
  box-shadow: 0 0 10px rgba(85, 85, 85, 0.3);
}

/* --- ERROR MESSAGES --- */
.error-message {
  color: var(--cyber-error) !important;
  font-weight: bold !important;
  font-style: normal !important;
  text-shadow: 0 0 5px rgba(255, 7, 58, 0.5);
  /* Убрана анимация подёргивания для ошибок */
}

/* --- MISC ELEMENTS --- */
.user-avatar-icon {
  width: 26px;
  height: 26px;
  color: #FFFFFF !important;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.8)) drop-shadow(0 0 6px rgba(0, 0, 0, 0.6));
  z-index: 3;
  position: relative;
  stroke: #FFFFFF !important;
  fill: none !important;
}

/* Elegant Icon Styles в цветовой палитре приложения */
.section-icon {
  width: 24px;
  height: 24px;
  color: #FFD700; /* Золотой для заголовков секций */
  margin-right: 12px;
  vertical-align: middle;
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
}

.block-icon {
  width: 20px;
  height: 20px;
  color: #FFA500; /* Оранжевый для заголовков блоков */
  margin-right: 8px;
  vertical-align: middle;
  filter: drop-shadow(0 0 2px rgba(255, 165, 0, 0.3));
}

.refresh-icon {
  width: 16px;
  height: 16px;
  color: #FF8C00; /* Темно-оранжевый для кнопок обновления */
  transition: transform 0.3s ease;
  filter: drop-shadow(0 0 2px rgba(255, 140, 0, 0.3));
}

.copy-icon {
  width: 16px;
  height: 16px;
  color: #DAA520; /* Золотисто-желтый для кнопки копирования */
  filter: drop-shadow(0 0 2px rgba(218, 165, 32, 0.3));
}

/* Button icon styles - fixed sizes */
.action-button svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

/* Icon animations */
.refresh-history-btn:hover .refresh-icon,
.action-button:hover .refresh-icon {
  transform: rotate(180deg);
}

/* Section and block title icons */
h2 .section-icon,
h3 .block-icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
}

h2 {
  display: flex;
  align-items: center;
  justify-content: center;
}

h3 {
  display: flex;
  align-items: center;
}

/* Currency icon styles */
.currency-icon {
  width: 20px;
  height: 20px;
  color: #FFD700;
  margin-right: 8px;
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
}

/* Status icon styles */
.status-icon svg {
  width: 20px;
  height: 20px;
  color: #FFA500;
  filter: drop-shadow(0 0 3px rgba(255, 165, 0, 0.3));
}

.clickable-balance {
  position: absolute;
  right: 9px;
  top: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-balance:hover {
  transform: scale(1.05);
}

/* --- RESPONSIVE DESIGN --- */
@media (max-width: 480px) {
  .currency-tabs-header {
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 6px;
    gap: 4px;
  }

  .currency-tab {
    min-width: 80px;
    flex-shrink: 1;
    padding: 14px 8px;
  }

  .tab-icon {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .tab-name {
    font-size: 11px;
    letter-spacing: 0.3px;
  }

  .tab-symbol {
    font-size: 9px;
  }
}

/* Для очень маленьких экранов */
@media (max-width: 360px) {
  .currency-tab {
    min-width: 75px;
    padding: 12px 6px;
  }

  .tab-icon {
    font-size: 18px;
  }

  .tab-name {
    font-size: 10px;
  }

  .tab-symbol {
    font-size: 8px;
  }
}

.app-main h2,
.app-section h2 {
  font-size: 20px;
}

.action-button {
  padding: 14px 20px;
  font-size: 15px;
}

/* --- LEGACY COMPATIBILITY --- */
.icon {
  display: inline-block;
  width: var(--icon-width);
  height: var(--icon-height);
  background-repeat: no-repeat;
  vertical-align: middle;
  background-size: var(--sprite-total-width) var(--sprite-total-height);
}

/* Icon positions - keeping original sprite positions */
.icon-energy {
  background-position: 0 0;
}
.icon-money {
  background-position: calc(var(--icon-width) * -1) 0;
}
.icon-ruble {
  background-position: calc(var(--icon-width) * -2) 0;
}
.icon-link {
  background-position: 0 calc(var(--icon-height) * -1);
}
.icon-play {
  background-position: calc(var(--icon-width) * -1)
    calc(var(--icon-height) * -1);
}
.icon-video-camera {
  background-position: calc(var(--icon-width) * -2)
    calc(var(--icon-height) * -1);
}
.icon-home {
  background-position: 0 calc(var(--icon-height) * -2);
}
.icon-dollar {
  background-position: calc(var(--icon-width) * -1)
    calc(var(--icon-height) * -2);
}
.icon-friends {
  background-position: calc(var(--icon-width) * -2)
    calc(var(--icon-height) * -2);
}

/* Стили для отслеживания выплат */
.withdrawal-date .text-muted {
  color: #666;
  font-size: 11px;
  margin-left: 8px;
}

.withdrawal-hash {
  color: #999;
  font-size: 11px;
  font-family: "Orbitron", monospace;
  margin-top: 5px;
}

.withdrawal-payout-id {
  color: var(--cyber-text-secondary);
  font-size: 11px;
  font-family: "Orbitron", monospace;
  opacity: 0.8;
  margin-top: 4px;
}

/* Кнопка отслеживания */
.track-button {
  background: linear-gradient(45deg, #FFD54F, #FFC107);
  border: none;
  color: #2C2C2C;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
  text-transform: uppercase;
  font-family: "Inter", sans-serif;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.track-button:hover {
  background: linear-gradient(45deg, #FFEB3B, #FFD54F);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.track-button:active {
  transform: scale(0.95);
}

/* Анимация обновления статуса */
.history-item.status-updated {
  animation: statusUpdate 1s ease-in-out;
}

@keyframes statusUpdate {
  0% {
    background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
    border-color: rgba(255, 193, 7, 0.3);
  }
  50% {
    background: linear-gradient(145deg, #FFEB3B, #FFD54F);
    border-color: #FFC107;
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
  }
  100% {
    background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
    border-color: rgba(255, 193, 7, 0.3);
  }
}

/* --- WITHDRAWAL RECOMMENDATIONS STYLES --- */
.withdrawal-recommendations {
  margin: 15px 0;
  padding: 15px;
  background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
}

.recommendations-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--cyber-text-primary);
  font-family: "Orbitron", monospace;
}

.help-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  width: 24px;
  height: 24px;
  position: relative;
}

.help-icon:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: scale(1.1);
  color: var(--text-primary);
}

.help-icon svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
}

/* Tooltip positioned above the help icon */
.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  z-index: 1000;
  background: var(--card-bg);
  border: 1px solid var(--border-medium);
  border-radius: 12px;
  padding: 16px;
  width: 300px;
  max-width: 90vw;
  box-shadow: 0 8px 24px var(--shadow-strong);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: normal;
  text-align: left;
}

/* Tooltip arrow */
.tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -6px;
  border: 6px solid transparent;
  border-top-color: var(--border-medium);
}

.tooltip::before {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -7px;
  border: 7px solid transparent;
  border-top-color: var(--card-bg);
  z-index: -1;
}

.tooltip p {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  color: var(--text-secondary);
}

/* Show tooltip on hover and when active */
.help-icon:hover .tooltip,
.tooltip.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
  pointer-events: auto;
}

/* Responsive positioning */
@media (max-width: 480px) {
  .tooltip {
    width: 280px;
    max-width: 95vw;
    font-size: 12px;
    padding: 12px;
    /* Проверяем, не выходит ли подсказка за границы экрана */
    left: 50%;
    transform: translateX(-50%);
  }

  /* Если подсказка выходит за правый край экрана */
  .tooltip {
    right: auto;
  }

  /* Стрелочка всегда по центру относительно кнопки */
  .tooltip::after {
    left: 50%;
    margin-left: -6px;
  }

  .tooltip::before {
    left: 50%;
    margin-left: -7px;
  }
}

/* Animation for smooth appearance */
@keyframes tooltipSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
  }
}

.tooltip.show {
  animation: tooltipSlideIn 0.3s ease-out;
}
p.calculator-subtitle {
    width: 100%;
    text-align: center;
}
.calculator-header {
    flex-direction: column;
    justify-content: center;
}
/* === WITHDRAWAL HISTORY STYLES === */
.withdrawal-history {
    margin-top: 15px;
}

.history-container {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 16px var(--shadow-light);
}

/* Стили для карточек выплат */
.withdrawal-item {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.withdrawal-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #FF8C00, #FFA500, #FFD700);
    border-radius: 16px 16px 0 0;
}

.withdrawal-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--shadow-medium);
    border-color: #FFA500;
}

.withdrawal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.withdrawal-amount {
    font-size: 18px;
    font-weight: bold;
    color: #FFA500;
    font-family: "Orbitron", monospace;
    text-shadow: 0 0 8px rgba(255, 165, 0, 0.4);
}

.withdrawal-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed, .status-finished {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(102, 187, 106, 0.1));
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.4);
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.2);
    font-weight: 600;
}

.status-pending, .status-processing {
    background: linear-gradient(135deg, rgba(255, 170, 0, 0.2), rgba(255, 140, 0, 0.1));
    color: #ffaa00;
    border: 1px solid rgba(255, 170, 0, 0.4);
    box-shadow: 0 0 8px rgba(255, 170, 0, 0.2);
    font-weight: 600;
}

.status-failed, .status-rejected {
    background: linear-gradient(135deg, rgba(255, 68, 68, 0.2), rgba(200, 50, 50, 0.1));
    color: #ff4444;
    border: 1px solid rgba(255, 68, 68, 0.4);
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.2);
    font-weight: 600;
}

.withdrawal-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--cyber-text-secondary);
    margin-bottom: 6px;
}

.withdrawal-currency {
    font-weight: bold;
    color: #FFD700;
    text-transform: uppercase;
    text-shadow: 0 0 6px rgba(255, 215, 0, 0.3);
}

.withdrawal-date {
    color: #FFB366;
    font-size: 12px;
    opacity: 0.8;
}

.withdrawal-address {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #FFA500;
    word-break: break-all;
    background: linear-gradient(135deg, rgba(255, 140, 0, 0.1), rgba(255, 165, 0, 0.05));
    border: 1px solid rgba(255, 165, 0, 0.2);
    padding: 6px 8px;
    border-radius: 6px;
    margin-top: 8px;
}

.withdrawal-success-info, .withdrawal-error-info, .withdrawal-pending-info {
    margin-top: 8px;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

.withdrawal-success-info {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.withdrawal-error-info {
    background: rgba(255, 68, 68, 0.1);
    color: #ff4444;
    border: 1px solid rgba(255, 68, 68, 0.2);
}

.withdrawal-pending-info {
    background: rgba(255, 170, 0, 0.1);
    color: #ffaa00;
    border: 1px solid rgba(255, 170, 0, 0.2);
}

.withdrawal-blockchain-link a {
    color: #FF9800;
    text-decoration: none;
    font-size: 11px;
}

.withdrawal-blockchain-link a:hover {
    color: var(--cyber-accent-neon);
    text-decoration: underline;
}

.cancel-button {
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.cancel-button:hover {
    background: linear-gradient(135deg, #ff6666, #ff0000);
    transform: scale(1.05);
}

/* Адаптивные стили для очень маленьких экранов */
@media (max-width: 360px) {
    .withdrawal-item {
        padding: 8px;
        font-size: 12px;
    }

    .withdrawal-amount {
        font-size: 14px;
    }

    .withdrawal-status {
        padding: 3px 6px;
        font-size: 10px;
    }

    .withdrawal-address {
        font-size: 9px;
        padding: 3px 4px;
    }

    .withdrawal-details {
        font-size: 11px;
    }
}

/* Стили для кнопки обновления истории */
.refresh-history-btn {
    background: linear-gradient(145deg, #FFD54F, #FFC107);
    color: #2C2C2C;
    border: none;
    padding: 12px 20px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: "Inter", sans-serif;
    box-shadow:
        0 6px 12px rgba(255, 193, 7, 0.4),
        0 3px 6px rgba(255, 193, 7, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -2px 0 rgba(255, 152, 0, 0.3);
}

.refresh-history-btn:hover {
    transform: translateY(-2px);
    background: linear-gradient(145deg, #FFEB3B, #FFD54F);
    box-shadow:
        0 8px 16px rgba(255, 193, 7, 0.5),
        0 4px 8px rgba(255, 193, 7, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.5),
        inset 0 -2px 0 rgba(255, 152, 0, 0.4);
}

.refresh-history-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px var(--shadow-light);
}

/* Улучшенная прокрутка для истории */
.history-list {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 193, 7, 0.3) transparent;
}

.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: rgba(255, 193, 7, 0.1);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #FFD54F, #FFC107);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #FFEB3B, #FFD54F);
}

/* Заголовки таблицы больше не нужны - используем карточный формат */

.history-list {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    align-items: center;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item > span {
    flex: 1;
    text-align: center;
    font-size: 14px;
}

.status-pending {
    color: #FFD700; /* золотой */
}

.status-confirmed {
    color: #4CAF50; /* зеленый */
}

.status-failed {
    color: #FF0000; /* красный */
}

.history-placeholder {
    padding: 20px;
    text-align: center;
    color: var(--cyber-text-secondary);
    font-style: italic;
}

/* === ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ О ВЫПЛАТАХ === */
.withdrawal-success-info {
  color: #4CAF50;
  font-size: 12px;
  margin-top: 8px;
  padding: 6px 10px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 4px;
  border-left: 3px solid #4CAF50;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.withdrawal-error-info {
  color: #ff4444;
  font-size: 12px;
  margin-top: 8px;
  padding: 6px 10px;
  background: rgba(255, 68, 68, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ff4444;
  font-family: "Rajdhani", sans-serif;
  font-weight: 500;
}

.withdrawal-pending-info {
  color: #ffaa00;
  font-size: 12px;
  margin-top: 8px;
  padding: 6px 10px;
  background: rgba(255, 170, 0, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ffaa00;
  font-family: "Rajdhani", sans-serif;
  font-weight: 500;
}

.withdrawal-blockchain-link {
  margin-top: 6px;
}

.withdrawal-blockchain-link a {
  color: #FF9800;
  text-decoration: none;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 152, 0, 0.1);
  border-radius: 3px;
  border: 1px solid rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.withdrawal-blockchain-link a:hover {
  background: rgba(255, 152, 0, 0.2);
  border-color: rgba(255, 152, 0, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

div:nth-child(3) > div.amount-input-section > div.input-group > span {
    display: none;
}

input#calc-amount {
    padding: 18px 9px 18px 10px;
}

.amount-input-section {
    padding: 24px 18px;
}

/* Удалены дублирующиеся стили - используем основные стили выше */