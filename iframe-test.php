<?php
// Принудительно устанавливаем заголовки для iframe
header('X-Frame-Options: ALLOWALL');
header('Content-Security-Policy: frame-ancestors *');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Тест iframe для Telegram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            padding: 20px;
            margin: 0;
            text-align: center;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #00ffff;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            font-weight: bold;
        }
        .success {
            background: rgba(0, 255, 0, 0.2);
            border: 2px solid #00ff00;
            color: #00ff88;
        }
        .info {
            background: rgba(0, 255, 255, 0.2);
            border: 2px solid #00ffff;
            color: #00ffff;
        }
        .error {
            background: rgba(255, 0, 0, 0.2);
            border: 2px solid #ff0000;
            color: #ff6666;
        }
        .code {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Тест iframe</h1>
        <p>Проверка настроек для Telegram мини-приложения</p>

        <div class="status success">
            ✅ Страница загружена
        </div>

        <div class="status info">
            📋 Заголовки HTTP:
        </div>

        <div class="code">
            <?php
            echo "X-Frame-Options: ALLOWALL\n";
            echo "Content-Security-Policy: frame-ancestors *\n";
            echo "Access-Control-Allow-Origin: *\n";
            echo "Время: " . date('Y-m-d H:i:s') . "\n";
            echo "Сервер: " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "\n";
            ?>
        </div>

        <div id="iframe-status" class="status info">
            🔍 Проверяем iframe...
        </div>

        <div class="status info">
            📱 Для тестирования:
            <br>1. Откройте @uniqpaid_paid_bot
            <br>2. Нажмите "Запустить приложение"
            <br>3. Если видите эту страницу - iframe работает!
        </div>

        <script>
            // Проверяем, загружены ли мы в iframe
            const iframeStatus = document.getElementById('iframe-status');
            
            if (window.self !== window.top) {
                // Мы в iframe - это хорошо!
                iframeStatus.className = 'status success';
                iframeStatus.innerHTML = '🎉 IFRAME РАБОТАЕТ! Загружено в Telegram!';
                document.body.style.border = '3px solid #00ff00';
                
                // Проверяем Telegram WebApp API
                if (window.Telegram && window.Telegram.WebApp) {
                    const telegramStatus = document.createElement('div');
                    telegramStatus.className = 'status success';
                    telegramStatus.innerHTML = '✅ Telegram WebApp API доступен!';
                    document.querySelector('.container').appendChild(telegramStatus);
                }
            } else {
                // Загружено напрямую
                iframeStatus.className = 'status info';
                iframeStatus.innerHTML = '📄 Загружено напрямую (не в iframe)';
            }

            // Показываем информацию о User-Agent
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Telegram')) {
                const telegramInfo = document.createElement('div');
                telegramInfo.className = 'status success';
                telegramInfo.innerHTML = '📱 Обнаружен Telegram User-Agent!';
                document.querySelector('.container').appendChild(telegramInfo);
            }

            // Автообновление каждые 5 секунд для мониторинга
            setTimeout(() => {
                const timeElement = document.createElement('div');
                timeElement.className = 'status info';
                timeElement.innerHTML = '🔄 Страница работает стабильно';
                document.querySelector('.container').appendChild(timeElement);
            }, 5000);
        </script>
    </div>
</body>
</html>
