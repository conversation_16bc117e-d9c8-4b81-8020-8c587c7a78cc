<?php
/**
 * api/getCurrencyData.php
 * API для получения актуальных данных о валютах с минимумами и комиссиями
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Подключаем конфигурацию
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

try {
    // Создаем экземпляр API для получения актуальных минимумов
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Базовые данные о валютах (синхронизированы с main.js)
    $currencyData = [
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 250, // Будет обновлено из API
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 5, // Будет обновлено из API
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 8580, // Будет обновлено из API
            'networkFee' => 5.58,
            'status' => 'expensive'
        ],
        'trx' => [
            'name' => 'TRON (TRX)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.30,
            'status' => 'good'
        ],
        'ltc' => [
            'name' => 'Litecoin (LTC)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'bch' => [
            'name' => 'Bitcoin Cash (BCH)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.30,
            'status' => 'good'
        ],
        'xrp' => [
            'name' => 'Ripple (XRP)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.20,
            'status' => 'good'
        ]
    ];
    
    // Получаем актуальные курсы валют
    $exchangeRates = [];
    foreach ($currencyData as $currency => $data) {
        try {
            if ($currency === 'usdttrc20') {
                $exchangeRates[$currency] = 1.0; // USDT = 1 USD
            } else {
                $rate = $api->getEstimateAmount(1, $currency, 'usd');
                if ($rate && isset($rate['estimated_amount'])) {
                    $exchangeRates[$currency] = $rate['estimated_amount'];
                    error_log("getCurrencyData INFO: Актуальный курс {$currency}: {$rate['estimated_amount']} USD");
                }
            }
        } catch (Exception $e) {
            error_log("getCurrencyData WARNING: Не удалось получить курс {$currency}: " . $e->getMessage());
            // Используем fallback курсы
            $fallbackRates = [
                'eth' => 2630,
                'btc' => 43000,
                'trx' => 0.12,
                'ltc' => 73,
                'bch' => 250
            ];
            $exchangeRates[$currency] = $fallbackRates[$currency] ?? 1;
        }
    }

    // Обновляем минимумы из API NOWPayments
    foreach ($currencyData as $currency => &$data) {
        try {
            $apiMinimum = $api->getMinWithdrawalAmount($currency);
            if ($apiMinimum !== null) {
                // Конвертируем API минимум из криптовалюты в USD
                $apiMinInUsd = $apiMinimum;
                if ($currency !== 'usdttrc20' && isset($exchangeRates[$currency])) {
                    $apiMinInUsd = $apiMinimum * $exchangeRates[$currency];
                }

                // Рассчитываем минимум в USD с учетом комиссии
                // Формула: (минимум_API + комиссия) = сумма_в_монетах * 0.001 - комиссия
                // Отсюда: сумма_в_монетах = (минимум_API + 2*комиссия) / 0.001
                $requiredUsdBeforeFee = $apiMinInUsd + (2 * $data['networkFee']);

                // Конвертируем в монеты с запасом
                $minInCoins = ceil($requiredUsdBeforeFee / CONVERSION_RATE);

                // Добавляем 10% запас для надежности
                $minInCoins = ceil($minInCoins * 1.1);

                // Используем рассчитанный минимум
                $data['minCoins'] = max($minInCoins, 1);
                $data['api_minimum'] = $apiMinimum;
                $data['api_min_usd'] = $apiMinInUsd;
                $data['required_usd_before_fee'] = $requiredUsdBeforeFee;

                // Обновляем комиссию на основе актуального курса
                if (isset($exchangeRates[$currency])) {
                    $data['current_rate'] = $exchangeRates[$currency];
                }

                error_log("getCurrencyData INFO: {$currency} - API минимум: {$apiMinimum}, USD: {$minInUsd}, монеты: {$minInCoins}, итого: {$data['minCoins']}");
            }
        } catch (Exception $e) {
            error_log("getCurrencyData WARNING: Ошибка получения минимума для {$currency}: " . $e->getMessage());
            // Добавляем fallback курс
            if (isset($exchangeRates[$currency])) {
                $data['current_rate'] = $exchangeRates[$currency];
            }
        }
    }
    
    // Добавляем информацию о том, показывать ли комиссии
    $showFees = defined('SHOW_FEES_TO_USER') ? SHOW_FEES_TO_USER : true;
    
    // Возвращаем обновленные данные
    echo json_encode([
        'success' => true,
        'currencies' => $currencyData,
        'exchange_rates' => $exchangeRates,
        'show_fees' => $showFees,
        'conversion_rate' => CONVERSION_RATE,
        'updated_at' => time(),
        'message' => 'Данные о валютах успешно загружены с актуальными курсами'
    ]);
    
} catch (Exception $e) {
    error_log("getCurrencyData ERROR: " . $e->getMessage());
    
    // В случае ошибки возвращаем базовые данные
    $fallbackData = [
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 250,
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 5,
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 8580,
            'networkFee' => 5.58,
            'status' => 'expensive'
        ],
        'trx' => [
            'name' => 'TRON (TRX)',
            'minCoins' => 1000,
            'networkFee' => 0.30,
            'status' => 'good'
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'currencies' => $fallbackData,
        'show_fees' => true,
        'conversion_rate' => 0.001,
        'updated_at' => time(),
        'message' => 'Использованы данные по умолчанию из-за ошибки API',
        'error' => $e->getMessage()
    ]);
}
?>
