# 🔍 Отладка Telegram Mini-App в веб-версии

## 🎯 **Как отладить мини-приложение в веб-версии Telegram**

### 1️⃣ **Открытие веб-версии Telegram**

#### **Способ 1: Через браузер**
```
https://web.telegram.org/k/
```
или
```
https://web.telegram.org/a/
```

#### **Способ 2: Telegram Desktop с веб-движком**
- Скачать Telegram Desktop
- В настройках включить "Использовать системный браузер для веб-приложений"

### 2️⃣ **Запуск мини-приложения**

1. **Найти бота** @uniqpaid_paid_bot
2. **Отправить команду** `/start`
3. **Нажать кнопку** "Запустить приложение" или "Launch App"
4. **Мини-приложение откроется** в iframe внутри Telegram

### 3️⃣ **Открытие инструментов разработчика**

#### **В Chrome/Edge:**
- **F12** или **Ctrl+Shift+I**
- Или **Правый клик** → "Inspect" / "Исследовать элемент"

#### **В Firefox:**
- **F12** или **Ctrl+Shift+I**
- Или **Правый клик** → "Inspect Element"

#### **В Safari:**
- **Cmd+Option+I** (Mac)
- Предварительно включить "Разработка" в настройках

### 4️⃣ **Поиск iframe с мини-приложением**

#### **В Elements/Inspector:**
1. **Найти iframe** с вашим приложением:
   ```html
   <iframe src="https://app.uniqpaid.com/test3/" ...>
   ```

2. **Правый клик на iframe** → "Inspect frame" / "Исследовать фрейм"

3. **Или использовать селектор** в консоли:
   ```javascript
   // Найти все iframe
   document.querySelectorAll('iframe')
   
   // Найти iframe с вашим доменом
   Array.from(document.querySelectorAll('iframe')).find(f => 
     f.src.includes('app.uniqpaid.com')
   )
   ```

### 5️⃣ **Переключение контекста на iframe**

#### **В Chrome DevTools:**
1. **Открыть Console**
2. **В выпадающем меню** (рядом с "top") выбрать iframe с вашим приложением
3. **Теперь консоль работает** в контексте мини-приложения

#### **В Firefox:**
1. **Открыть Console**
2. **Кликнуть на иконку** "Select an iframe as the currently targeted document"
3. **Выбрать iframe** с вашим приложением

### 6️⃣ **Отладка JavaScript**

#### **Проверка ошибок:**
```javascript
// В консоли iframe
console.log('Мини-приложение загружено');

// Проверить Telegram WebApp API
console.log(window.Telegram);
console.log(window.Telegram.WebApp);

// Проверить пользователя
console.log(window.Telegram.WebApp.initDataUnsafe.user);
```

#### **Проверка сетевых запросов:**
1. **Открыть вкладку Network**
2. **Обновить мини-приложение**
3. **Смотреть все запросы** к вашему API

#### **Проверка локального хранилища:**
```javascript
// localStorage
console.log(localStorage);

// sessionStorage
console.log(sessionStorage);

// Cookies
console.log(document.cookie);
```

### 7️⃣ **Отладка CSS и верстки**

#### **Проверка стилей:**
1. **Elements tab** → выбрать элемент
2. **Styles panel** → смотреть применяемые стили
3. **Computed panel** → итоговые вычисленные стили

#### **Адаптивность:**
1. **Device Toolbar** (Ctrl+Shift+M)
2. **Выбрать размер экрана** мобильного устройства
3. **Проверить как выглядит** на разных разрешениях

### 8️⃣ **Специфичные проблемы Telegram WebApp**

#### **Проверка X-Frame-Options:**
```javascript
// В консоли основной страницы (не iframe)
fetch('https://app.uniqpaid.com/test3/')
  .then(response => {
    console.log('X-Frame-Options:', response.headers.get('X-Frame-Options'));
    console.log('Все заголовки:', [...response.headers.entries()]);
  });
```

#### **Проверка CORS:**
```javascript
// В консоли iframe
fetch('/api/getUserData.php')
  .then(response => response.json())
  .then(data => console.log('API работает:', data))
  .catch(error => console.error('CORS ошибка:', error));
```

#### **Проверка Telegram WebApp API:**
```javascript
// Проверить доступность API
if (window.Telegram && window.Telegram.WebApp) {
  console.log('✅ Telegram WebApp API доступен');
  console.log('Версия:', window.Telegram.WebApp.version);
  console.log('Платформа:', window.Telegram.WebApp.platform);
  console.log('Пользователь:', window.Telegram.WebApp.initDataUnsafe.user);
} else {
  console.error('❌ Telegram WebApp API недоступен');
}
```

### 9️⃣ **Эмуляция мобильного устройства**

#### **Device Emulation:**
1. **F12** → **Toggle Device Toolbar** (Ctrl+Shift+M)
2. **Выбрать устройство:** iPhone, Android
3. **Установить User-Agent** мобильного браузера
4. **Проверить touch события**

#### **Эмуляция Telegram среды:**
```javascript
// Добавить в начало вашего main.js для тестирования
if (!window.Telegram) {
  window.Telegram = {
    WebApp: {
      initData: '',
      initDataUnsafe: {
        user: {
          id: 123456789,
          first_name: 'Test',
          username: 'testuser',
          language_code: 'ru'
        }
      },
      version: '6.0',
      platform: 'web',
      ready: function() { console.log('WebApp ready'); },
      close: function() { console.log('WebApp close'); },
      expand: function() { console.log('WebApp expand'); }
    }
  };
}
```

### 🔟 **Полезные команды для отладки**

#### **Проверка загрузки ресурсов:**
```javascript
// Проверить все загруженные скрипты
Array.from(document.scripts).map(s => s.src);

// Проверить все стили
Array.from(document.styleSheets).map(s => s.href);

// Проверить все изображения
Array.from(document.images).map(i => i.src);
```

#### **Проверка API запросов:**
```javascript
// Тест API
async function testAPI() {
  try {
    const response = await fetch('/api/getUserData.php', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ user_id: 123456789 })
    });
    const data = await response.json();
    console.log('API ответ:', data);
  } catch (error) {
    console.error('API ошибка:', error);
  }
}
testAPI();
```

### 1️⃣1️⃣ **Решение частых проблем**

#### **Мини-приложение не загружается:**
1. **Проверить X-Frame-Options** в Network tab
2. **Проверить CORS заголовки**
3. **Проверить SSL сертификат**
4. **Очистить кэш браузера**

#### **JavaScript ошибки:**
1. **Console tab** → смотреть все ошибки
2. **Sources tab** → поставить breakpoints
3. **Проверить загрузку** всех JS файлов

#### **API не работает:**
1. **Network tab** → смотреть статус запросов
2. **Проверить CORS заголовки**
3. **Проверить формат данных**

### 1️⃣2️⃣ **Продвинутая отладка**

#### **Performance профилирование:**
1. **Performance tab**
2. **Record** → взаимодействовать с приложением → **Stop**
3. **Анализировать** узкие места

#### **Memory профилирование:**
1. **Memory tab**
2. **Heap snapshot** → сделать снимок памяти
3. **Искать утечки памяти**

#### **Application данные:**
1. **Application tab**
2. **Local Storage** → данные приложения
3. **Session Storage** → временные данные
4. **Cookies** → куки

## 🎯 **Готово!**

Теперь ты можешь полноценно отлаживать мини-приложение прямо в веб-версии Telegram! 🚀

### 📋 **Быстрый чеклист:**
- ✅ Открыть web.telegram.org
- ✅ Запустить @uniqpaid_paid_bot
- ✅ F12 → найти iframe
- ✅ Переключить контекст на iframe
- ✅ Отлаживать как обычное веб-приложение

**Удачной отладки!** 💪
