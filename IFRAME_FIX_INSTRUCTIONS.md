# 🔧 Исправление проблемы с iframe для Telegram мини-приложения

## 🚨 Проблема
```
Refused to display 'https://app.uniqpaid.com/' in a frame because it set 'X-Frame-Options' to 'deny'.
```

## ✅ Решение

### 1. Обновить основной `.htaccess` файл

Заменить секцию с заголовками безопасности:

```apache
# Защита от некоторых атак (адаптировано для Telegram мини-приложения)
<IfModule mod_headers.c>
    # Защита от XSS
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"
    
    # Альтернативно используем CSP для более точного контроля
    Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; img-src 'self' data: https:; font-src 'self' data: https:; frame-ancestors 'self' https://web.telegram.org https://telegram.org https://*.telegram.org;"
    
    # Разрешаем CORS для API запросов
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>
```

### 2. Обновить `api/.htaccess` файл

Добавить в начало файла:

```apache
# Настройки для работы с Telegram мини-приложением
<IfModule mod_headers.c>
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"
    
    # Разрешаем CORS для API запросов
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, X-Telegram-Init-Data"
    
    # Для preflight запросов
    Header always set Access-Control-Max-Age "3600"
</IfModule>

# Обработка preflight запросов
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
```

### 3. Проверить настройки сервера

Убедиться что включены модули Apache:
- `mod_headers`
- `mod_rewrite`

### 4. Перезапустить веб-сервер

```bash
# Apache
sudo systemctl restart apache2
# или
sudo service apache2 restart

# Nginx (если используется)
sudo systemctl restart nginx
```

### 5. Очистить кэш

- Очистить кэш браузера
- Очистить кэш CDN (если используется)
- Очистить кэш сервера

## 🧪 Тестирование

### Локальное тестирование
1. Открыть: `https://app.uniqpaid.com/test_headers.php`
2. Проверить заголовки в консоли разработчика

### Тестирование в Telegram
1. Открыть бота @uniqpaid_paid_bot
2. Нажать "Запустить приложение"
3. Проверить что мини-приложение загружается

## 🔍 Диагностика

### Проверка заголовков через curl:
```bash
curl -I https://app.uniqpaid.com/
```

Должно содержать:
```
X-Frame-Options: ALLOWALL
Access-Control-Allow-Origin: *
```

### Проверка в браузере:
1. Открыть DevTools (F12)
2. Перейти на вкладку Network
3. Обновить страницу
4. Проверить заголовки ответа

## ⚠️ Важные моменты

1. **Безопасность**: `X-Frame-Options: ALLOWALL` разрешает встраивание сайта в любые iframe. Для большей безопасности можно использовать:
   ```apache
   Header always set X-Frame-Options "SAMEORIGIN"
   ```
   Но это может не работать с Telegram.

2. **CSP**: Content-Security-Policy с `frame-ancestors` обеспечивает более точный контроль.

3. **CORS**: Необходим для API запросов из мини-приложения.

## 🚀 После применения

Мини-приложение должно:
- ✅ Загружаться в Telegram без ошибок
- ✅ Отображать корректный баланс (497 монет)
- ✅ Показывать историю выплат (2 карточки)
- ✅ Работать все API функции

## 📞 Поддержка

Если проблема остается:
1. Проверить логи сервера
2. Убедиться что mod_headers включен
3. Проверить синтаксис .htaccess файлов
4. Связаться с хостинг-провайдером

---

**Статус**: ✅ Готово к применению на продакшн сервере
