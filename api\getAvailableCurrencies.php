<?php
/**
 * api/getAvailableCurrencies.php
 * API эндпоинт для получения списка доступных криптовалют для вывода.
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in getAvailableCurrencies.php'); 
    echo json_encode(['error'=>'Ошибка сервера: CFG']); 
    exit; 
}
if (!(@require_once __DIR__ . '/validate_initdata.php')) { 
    http_response_code(500); 
    error_log('FATAL: validate_initdata.php not found in getAvailableCurrencies.php'); 
    echo json_encode(['error'=>'Ошибка сервера: VID']); 
    exit; 
}
if (!(@require_once __DIR__ . '/nowpayments_api.php')) { 
    http_response_code(500); 
    error_log('FATAL: nowpayments_api.php not found in getAvailableCurrencies.php'); 
    echo json_encode(['error'=>'Ошибка сервера: NPAPI']); 
    exit; 
}
// --- Конец проверки зависимостей ---

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Нет данных']);
    exit;
}
$initData = $input['initData'];
error_log("getAvailableCurrencies INFO: Получен initData (длина: " . strlen($initData) . ")");

// 2. Валидация initData
$validatedData = validateTelegramInitData($initData);
if ($validatedData === false) {
    http_response_code(403);
    echo json_encode(['error' => 'Ошибка: Неверные данные']);
    exit;
}
$userId = intval($validatedData['user']['id']);
error_log("getAvailableCurrencies INFO: initData валидирован для user {$userId}");

// 3. Получение списка доступных криптовалют
try {
    // Создаем экземпляр API клиента
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Получаем список доступных криптовалют
    $currencies = $api->getCurrencies();
    
    if (!isset($currencies['currencies']) || !is_array($currencies['currencies'])) {
        error_log("getAvailableCurrencies ERROR: Не удалось получить список криптовалют");
        http_response_code(500);
        echo json_encode(['error' => 'Ошибка: Не удалось получить список криптовалют']);
        exit;
    }
    
    // Фильтруем список криптовалют (опционально)
    // Например, можно оставить только основные криптовалюты
    $popularCurrencies = ['BTC', 'ETH', 'LTC', 'DOGE', 'USDT', 'USDC', 'XRP', 'TRX', 'BNB'];
    $filteredCurrencies = array_filter($currencies['currencies'], function($currency) use ($popularCurrencies) {
        return in_array($currency, $popularCurrencies);
    });
    
    // Если после фильтрации список пуст, возвращаем все доступные валюты
    if (empty($filteredCurrencies)) {
        $filteredCurrencies = $currencies['currencies'];
    }
    
    // 4. Успешный ответ
    http_response_code(200);
    echo json_encode([
        'currencies' => $filteredCurrencies
    ]);
    error_log("getAvailableCurrencies INFO: Успешно отправлен список из " . count($filteredCurrencies) . " криптовалют");
    
} catch (Exception $e) {
    error_log("getAvailableCurrencies ERROR: Исключение при получении списка криптовалют: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка: ' . $e->getMessage()]);
    exit;
}
?>
