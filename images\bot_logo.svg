<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF8E7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFE4B5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </radialGradient>
    <filter id="shadow">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.15)"/>
    </filter>
  </defs>
  
  <!-- Фон с градиентом -->
  <circle cx="256" cy="256" r="256" fill="url(#bgGradient)"/>
  
  <!-- Свечение -->
  <circle cx="256" cy="256" r="200" fill="url(#glowGradient)"/>
  
  <!-- Основная монета -->
  <circle cx="256" cy="200" r="80" fill="url(#coinGradient)" stroke="#ffffff" stroke-width="6"/>
  
  <!-- Внутренний круг монеты -->
  <circle cx="256" cy="200" r="60" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.5"/>
  
  <!-- Элегантная иконка кошелька -->
  <g transform="translate(256, 200)" fill="#2C2C2C">
    <rect x="-30" y="-15" width="60" height="30" rx="6" fill="none" stroke="currentColor" stroke-width="4"/>
    <path d="M-30 -3h60" stroke="currentColor" stroke-width="4"/>
    <circle cx="18" cy="3" r="3" fill="currentColor"/>
    <path d="M-21 -15v-6a6 6 0 0 1 6-6h30a6 6 0 0 1 6 6v6" fill="none" stroke="currentColor" stroke-width="4"/>
  </g>
  
  <!-- Маленькие монеты -->
  <circle cx="160" cy="150" r="25" fill="url(#coinGradient)" opacity="0.8"/>
  <text x="160" y="160" font-family="Arial Black, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#ffffff">$</text>
  
  <circle cx="360" cy="170" r="30" fill="url(#coinGradient)" opacity="0.7"/>
  <text x="360" y="182" font-family="Arial Black, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#ffffff">$</text>
  
  <circle cx="180" cy="280" r="20" fill="url(#coinGradient)" opacity="0.6"/>
  <text x="180" y="288" font-family="Arial Black, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#ffffff">$</text>
  
  <circle cx="340" cy="260" r="22" fill="url(#coinGradient)" opacity="0.6"/>
  <text x="340" y="268" font-family="Arial Black, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#ffffff">$</text>
  
  <!-- Название бота -->
  <text x="256" y="340" font-family="Inter, sans-serif" font-size="42" font-weight="700" text-anchor="middle" fill="#2C2C2C">UniQ</text>
  <text x="256" y="380" font-family="Inter, sans-serif" font-size="42" font-weight="700" text-anchor="middle" fill="#FF8C00">Paid</text>

  <!-- Подзаголовок -->
  <text x="256" y="410" font-family="Inter, sans-serif" font-size="18" font-weight="500" text-anchor="middle" fill="#666666">CRYPTO WALLET</text>
  
  <!-- Декоративные звездочки -->
  <text x="120" y="380" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="#fbbf24" opacity="0.8">✨</text>
  <text x="390" y="350" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#fbbf24" opacity="0.6">✨</text>
  <text x="140" y="120" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#ffffff" opacity="0.7">✨</text>
  <text x="380" y="400" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#ffffff" opacity="0.5">✨</text>
  
  <!-- Иконка рекламы внизу -->
  <rect x="220" y="440" width="72" height="50" rx="6" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <circle cx="256" cy="465" r="8" fill="#ffffff"/>
  <polygon points="252,462 252,468 262,465" fill="#4f46e5"/>
  <text x="256" y="505" font-family="Arial, sans-serif" font-size="10" font-weight="bold" text-anchor="middle" fill="rgba(255,255,255,0.8)">ADS</text>
</svg>
